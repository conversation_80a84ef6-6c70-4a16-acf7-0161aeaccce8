"""
认证相关Pydantic模式
"""

from pydantic import BaseModel, EmailStr
from typing import Optional


class Token(BaseModel):
    """访问令牌响应"""
    access_token: str
    token_type: str
    expires_in: int


class TokenData(BaseModel):
    """令牌数据"""
    username: Optional[str] = None


class UserLogin(BaseModel):
    """用户登录请求"""
    username: str
    password: str


class UserCreate(BaseModel):
    """用户创建请求"""
    username: str
    email: EmailStr
    password: str
    full_name: Optional[str] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "johndo<PERSON>",
                "email": "<EMAIL>",
                "password": "secretpassword",
                "full_name": "<PERSON>"
            }
        }
