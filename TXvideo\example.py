#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫使用示例
"""

import os
import sys
from tx_video_spider import TencentVideoSpider
from utils import Logger, ConfigValidator, format_size
from config import LOG_CONFIG, DOWNLOAD_CONFIG


def example_single_video():
    """单个视频下载示例"""
    print("=== 单个视频下载示例 ===")
    
    # 初始化爬虫
    spider = TencentVideoSpider()
    
    # 示例视频URL（请替换为实际的腾讯视频URL）
    video_url = input("请输入腾讯视频URL: ").strip()
    
    if not video_url:
        print("URL不能为空！")
        return
    
    # 验证URL
    if not ConfigValidator.validate_url(video_url):
        print("URL格式不正确！")
        return
    
    # 设置输出文件名
    output_file = input("请输入输出文件名（默认: video.mp4）: ").strip()
    if not output_file:
        output_file = "video.mp4"
    
    # 确保输出目录存在
    output_dir = DOWNLOAD_CONFIG['OUTPUT_DIR']
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    output_path = os.path.join(output_dir, output_file)
    
    # 开始下载
    print(f"开始下载视频到: {output_path}")
    success = spider.crawl_video(video_url, output_path)
    
    if success:
        print("✅ 视频下载成功！")
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"文件大小: {format_size(file_size)}")
    else:
        print("❌ 视频下载失败！")


def example_batch_download():
    """批量下载示例"""
    print("=== 批量下载示例 ===")
    
    # 初始化爬虫
    spider = TencentVideoSpider()
    
    # 视频URL列表（请替换为实际的腾讯视频URL）
    video_urls = [
        "https://v.qq.com/x/cover/example1/video1.html",
        "https://v.qq.com/x/cover/example2/video2.html",
        # 添加更多URL...
    ]
    
    print(f"准备下载 {len(video_urls)} 个视频")
    
    success_count = 0
    failed_count = 0
    
    for i, url in enumerate(video_urls, 1):
        print(f"\n[{i}/{len(video_urls)}] 正在处理: {url}")
        
        # 生成输出文件名
        output_file = f"video_{i:03d}.mp4"
        output_path = os.path.join(DOWNLOAD_CONFIG['OUTPUT_DIR'], output_file)
        
        # 下载视频
        success = spider.crawl_video(url, output_path)
        
        if success:
            success_count += 1
            print(f"✅ 下载成功: {output_file}")
        else:
            failed_count += 1
            print(f"❌ 下载失败: {url}")
    
    print(f"\n=== 下载完成 ===")
    print(f"成功: {success_count} 个")
    print(f"失败: {failed_count} 个")


def example_extract_info_only():
    """仅提取视频信息示例"""
    print("=== 提取视频信息示例 ===")
    
    spider = TencentVideoSpider()
    
    video_url = input("请输入腾讯视频URL: ").strip()
    
    if not video_url:
        print("URL不能为空！")
        return
    
    # 提取基本信息
    video_info = spider.extract_video_info(video_url)
    if not video_info:
        print("❌ 提取视频信息失败！")
        return
    
    print("📹 视频基本信息:")
    print(f"  VID: {video_info.get('vid', 'N/A')}")
    print(f"  Cover ID: {video_info.get('coverid', 'N/A')}")
    print(f"  URL: {video_info.get('url', 'N/A')}")
    
    # 生成请求参数
    params = spider.generate_params(video_info['vid'], video_info['coverid'])
    if params:
        print("\n🔧 请求参数:")
        print(f"  GUID: {params.get('guid', 'N/A')}")
        print(f"  Platform: {params.get('platform', 'N/A')}")
        print(f"  App Version: {params.get('app_ver', 'N/A')}")
        print(f"  cKey: {params.get('ckey', 'N/A')[:50]}...")
    
    # 获取详细视频信息
    video_data = spider.get_video_info(params)
    if video_data:
        print("\n📊 视频详细信息获取成功")
        
        # 解析视频URL
        video_urls = spider.parse_video_urls(video_data)
        if video_urls:
            print(f"\n🎬 找到 {len(video_urls)} 个视频流:")
            for i, url in enumerate(video_urls, 1):
                print(f"  {i}. {url[:80]}...")
        else:
            print("\n❌ 未找到视频流URL")
    else:
        print("\n❌ 获取视频详细信息失败")


def example_custom_config():
    """自定义配置示例"""
    print("=== 自定义配置示例 ===")
    
    # 创建自定义配置的爬虫实例
    spider = TencentVideoSpider()
    
    # 修改请求头
    spider.session.headers.update({
        'User-Agent': 'Custom User Agent',
        'Custom-Header': 'Custom Value'
    })
    
    # 设置代理（如果需要）
    # spider.session.proxies = {
    #     'http': 'http://proxy.example.com:8080',
    #     'https': 'https://proxy.example.com:8080'
    # }
    
    print("✅ 自定义配置已应用")
    
    # 继续使用爬虫...
    video_url = input("请输入腾讯视频URL: ").strip()
    if video_url:
        success = spider.crawl_video(video_url)
        print("✅ 下载成功" if success else "❌ 下载失败")


def show_menu():
    """显示菜单"""
    print("\n" + "="*50)
    print("🎬 腾讯视频爬虫工具")
    print("="*50)
    print("1. 单个视频下载")
    print("2. 批量视频下载")
    print("3. 仅提取视频信息")
    print("4. 自定义配置示例")
    print("0. 退出")
    print("="*50)


def main():
    """主函数"""
    # 设置日志
    logger = Logger.setup_logger(
        'tx_spider_example',
        LOG_CONFIG.get('FILE'),
        LOG_CONFIG.get('LEVEL', 'INFO')
    )
    
    # 确保下载目录存在
    download_dir = DOWNLOAD_CONFIG['OUTPUT_DIR']
    if not os.path.exists(download_dir):
        os.makedirs(download_dir)
        print(f"✅ 创建下载目录: {download_dir}")
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择功能 (0-4): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                example_single_video()
            elif choice == '2':
                example_batch_download()
            elif choice == '3':
                example_extract_info_only()
            elif choice == '4':
                example_custom_config()
            else:
                print("❌ 无效选择，请重新输入！")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            logger.error(f"程序异常: {e}")
        
        input("\n按回车键继续...")


if __name__ == "__main__":
    main()
