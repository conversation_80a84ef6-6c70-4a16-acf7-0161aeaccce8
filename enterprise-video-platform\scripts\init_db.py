#!/usr/bin/env python3
"""
数据库初始化脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.database import create_tables, db_manager
from app.models.user import User
from app.models.video import Video, DownloadHistory, VideoCollection
from app.api.auth import get_password_hash
from app.config import get_settings
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_database():
    """初始化数据库"""
    try:
        logger.info("开始初始化数据库...")
        
        # 检查数据库连接
        if not db_manager.check_connection():
            logger.error("数据库连接失败")
            return False
        
        # 创建所有表
        create_tables()
        logger.info("数据库表创建成功")
        
        # 创建默认管理员用户
        create_default_admin()
        
        logger.info("数据库初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        return False


def create_default_admin():
    """创建默认管理员用户"""
    try:
        session = db_manager.get_session()
        
        # 检查是否已存在管理员
        admin = session.query(User).filter(User.username == "admin").first()
        if admin:
            logger.info("管理员用户已存在")
            return
        
        # 创建管理员用户
        admin_user = User(
            username="admin",
            email="<EMAIL>",
            hashed_password=get_password_hash("admin123"),
            full_name="系统管理员",
            is_active=True,
            is_superuser=True,
            is_verified=True
        )
        
        session.add(admin_user)
        session.commit()
        
        logger.info("默认管理员用户创建成功")
        logger.info("用户名: admin")
        logger.info("密码: admin123")
        logger.info("请及时修改默认密码！")
        
    except Exception as e:
        logger.error(f"创建管理员用户失败: {e}")
        session.rollback()
    finally:
        session.close()


def create_sample_data():
    """创建示例数据"""
    try:
        session = db_manager.get_session()
        
        # 创建示例视频
        sample_videos = [
            {
                "title": "示例视频1",
                "description": "这是一个示例视频",
                "original_url": "https://v.qq.com/x/cover/example1.html",
                "vid": "example1",
                "cover_id": "cover1",
                "duration": 3600,
                "quality": "1080p",
                "format": "mp4",
                "category": "电影",
                "is_free": True
            },
            {
                "title": "示例视频2", 
                "description": "这是另一个示例视频",
                "original_url": "https://v.qq.com/x/cover/example2.html",
                "vid": "example2",
                "cover_id": "cover2",
                "duration": 1800,
                "quality": "720p",
                "format": "mp4",
                "category": "电视剧",
                "is_free": False
            }
        ]
        
        for video_data in sample_videos:
            # 检查是否已存在
            existing = session.query(Video).filter(Video.original_url == video_data["original_url"]).first()
            if not existing:
                video = Video(**video_data)
                session.add(video)
        
        session.commit()
        logger.info("示例数据创建成功")
        
    except Exception as e:
        logger.error(f"创建示例数据失败: {e}")
        session.rollback()
    finally:
        session.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="数据库初始化脚本")
    parser.add_argument("--sample-data", action="store_true", help="创建示例数据")
    parser.add_argument("--reset", action="store_true", help="重置数据库(删除所有数据)")
    
    args = parser.parse_args()
    
    if args.reset:
        confirm = input("确定要重置数据库吗？这将删除所有数据 (y/N): ")
        if confirm.lower() == 'y':
            db_manager.drop_all_tables()
            logger.info("数据库已重置")
        else:
            logger.info("操作已取消")
            sys.exit(0)
    
    # 初始化数据库
    if init_database():
        if args.sample_data:
            create_sample_data()
        logger.info("数据库初始化完成")
    else:
        logger.error("数据库初始化失败")
        sys.exit(1)
