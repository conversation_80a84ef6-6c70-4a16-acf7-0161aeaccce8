# 腾讯视频爬虫框架

一个用于爬取腾讯视频的Python框架，支持视频信息提取和下载功能。

## ⚠️ 重要声明

**本项目仅供学习研究使用，请遵守相关法律法规和腾讯视频的服务条款。**

- 腾讯视频内容受版权保护
- 请勿用于商业用途
- 请勿大规模爬取
- 使用者需自行承担法律责任

## 🚀 功能特性

- ✅ 自动提取视频ID和封面ID
- ✅ 生成加密参数（cKey等）
- ✅ 获取视频流信息
- ✅ 支持M3U8格式视频下载
- ✅ 支持多种视频质量
- ✅ 批量下载功能
- ✅ 进度显示
- ✅ 错误重试机制
- ✅ 日志记录

## 📋 环境要求

- Python 3.7+
- Windows/Linux/macOS

## 🔧 安装依赖

```bash
pip install -r requirements.txt
```

### 主要依赖说明

- `requests`: HTTP请求库
- `pycryptodome`: AES加密库
- `PyExecJS`: JavaScript执行环境
- `urllib3`: URL处理库

## 📁 项目结构

```
TXvideo/
├── tx_video_spider.py    # 主爬虫类
├── config.py            # 配置文件
├── utils.py             # 工具函数
├── example.py           # 使用示例
├── requirements.txt     # 依赖包列表
├── README.md           # 说明文档
└── downloads/          # 下载目录（自动创建）
```

## 🎯 快速开始

### 1. 基本使用

```python
from tx_video_spider import TencentVideoSpider

# 创建爬虫实例
spider = TencentVideoSpider()

# 爬取视频
video_url = "https://v.qq.com/x/cover/example/video.html"
success = spider.crawl_video(video_url, "output.mp4")

if success:
    print("下载成功！")
else:
    print("下载失败！")
```

### 2. 运行示例程序

```bash
python example.py
```

### 3. 仅提取视频信息

```python
# 提取基本信息
video_info = spider.extract_video_info(video_url)
print(f"VID: {video_info['vid']}")
print(f"Cover ID: {video_info['coverid']}")

# 生成请求参数
params = spider.generate_params(video_info['vid'], video_info['coverid'])
print(f"cKey: {params['ckey']}")

# 获取视频详细信息
video_data = spider.get_video_info(params)
video_urls = spider.parse_video_urls(video_data)
print(f"找到 {len(video_urls)} 个视频流")
```

## 🔑 核心参数说明

### 必需参数

| 参数名 | 说明 | 示例 |
|--------|------|------|
| `vid` | 视频ID | `z002615k57t` |
| `coverid` | 封面/剧集ID | `j3czmhisqin799r` |
| `ckey` | 加密密钥 | `--01W5agxKnJ7N56...` |
| `guid` | 用户标识 | `4b4e192e83f4abaf...` |
| `platform` | 平台标识 | `10201` (PC端) |

### cKey生成算法

cKey是最关键的参数，生成过程：

1. 拼接字符串：`""|vid|timestamp|固定密钥|版本|guid|平台|ending`
2. 计算qa值：特定哈希算法
3. AES加密：使用固定密钥加密
4. 添加前缀：`--01` + 加密结果

## 🛠️ 配置说明

### 修改配置

编辑 `config.py` 文件：

```python
# 修改下载目录
DOWNLOAD_CONFIG['OUTPUT_DIR'] = './my_downloads/'

# 修改超时时间
DOWNLOAD_CONFIG['TIMEOUT'] = 60

# 修改重试次数
DOWNLOAD_CONFIG['RETRY_TIMES'] = 5
```

### 使用代理

```python
spider = TencentVideoSpider()
spider.session.proxies = {
    'http': 'http://proxy.example.com:8080',
    'https': 'https://proxy.example.com:8080'
}
```

## 🔍 F12调试指南

### 1. 打开开发者工具

1. 访问腾讯视频页面
2. 按F12打开开发者工具
3. 切换到Network标签

### 2. 查找关键请求

播放视频后，搜索以下关键词：

- `proxyhttp` - 主要API接口
- `m3u8` - 视频流地址
- `vinfo` - 视频信息

### 3. 提取参数

从 `proxyhttp` 请求中提取：

- Headers中的User-Agent
- POST body中的参数
- Response中的视频信息

## 📝 常见问题

### Q: 提示"无法提取vid"？

A: 检查视频URL格式，确保是有效的腾讯视频链接。

### Q: cKey生成失败？

A: 这是最常见的问题，可能需要：
- 更新加密算法
- 检查固定密钥是否正确
- 分析最新的JS代码

### Q: 下载速度慢？

A: 可以尝试：
- 使用代理服务器
- 调整并发数量
- 选择较低的视频质量

### Q: 视频无法播放？

A: 检查：
- 文件是否完整下载
- TS片段是否正确合并
- 视频格式是否支持

## 🔄 更新日志

### v1.0.0 (2025-01-30)
- ✅ 初始版本发布
- ✅ 基础爬虫功能
- ✅ 参数生成算法
- ✅ M3U8下载支持
- ✅ 批量下载功能

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交Issue和Pull Request！

## ⚖️ 免责声明

1. 本工具仅供学习和研究使用
2. 使用者需遵守当地法律法规
3. 不得用于侵犯版权的行为
4. 作者不承担任何法律责任

---

**再次提醒：请合法合规使用本工具！**
