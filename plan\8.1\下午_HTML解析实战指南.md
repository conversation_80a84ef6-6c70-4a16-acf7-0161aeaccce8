# 🕷️ HTML解析实战指南 (下午专用)

## 🎯 学习目标：从网页到数据的完整流程

### 📚 第一步：理解网页数据提取的本质 (10分钟)

**什么是HTML解析？**
- HTML = 网页的骨架结构
- 解析 = 从结构化文本中提取有用信息
- 目标 = 将人类可读的网页转换为程序可处理的数据

**生活类比**：
```
网页就像一份报纸：
- 标题在 <h1> 标签里 = 报纸的大标题
- 正文在 <p> 标签里 = 报纸的段落
- 链接在 <a> 标签里 = 报纸的参考资料

我们的任务：教会程序"读报纸"
```

**为什么需要HTML解析？**
- 获取实时数据（股价、天气、新闻）
- 监控竞品信息
- 收集研究数据
- 自动化信息收集

---

## 🛠️ 第二步：BeautifulSoup基础入门 (30分钟)

### 安装和导入
```bash
pip install beautifulsoup4 requests lxml html5lib
```

### 第一个解析示例
```python
# 文件：day2_bs4_intro.py
from bs4 import BeautifulSoup
import requests

# 示例HTML - 模拟豆瓣电影页面结构
html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>豆瓣电影 Top 250</title>
</head>
<body>
    <div class="grid_view">
        <div class="item" data-id="1292052">
            <div class="pic">
                <img src="movie1.jpg" alt="肖申克的救赎">
            </div>
            <div class="info">
                <div class="hd">
                    <a href="/subject/1292052/">
                        <span class="title">肖申克的救赎</span>
                        <span class="title">&nbsp;/&nbsp;The Shawshank Redemption</span>
                    </a>
                </div>
                <div class="bd">
                    <p class="">
                        导演: 弗兰克·德拉邦特&nbsp;&nbsp;&nbsp;
                        主演: 蒂姆·罗宾斯 / 摩根·弗里曼
                    </p>
                    <p class="">
                        1994&nbsp;/&nbsp;美国&nbsp;/&nbsp;犯罪 剧情
                    </p>
                </div>
                <div class="star">
                    <span class="rating5-t"></span>
                    <span class="rating_num" property="v:average">9.7</span>
                    <span property="v:best" content="10.0"></span>
                    <span>2677068人评价</span>
                </div>
                <p class="quote">
                    <span class="inq">希望让人自由。</span>
                </p>
            </div>
        </div>
        
        <div class="item" data-id="1291546">
            <div class="pic">
                <img src="movie2.jpg" alt="霸王别姬">
            </div>
            <div class="info">
                <div class="hd">
                    <a href="/subject/1291546/">
                        <span class="title">霸王别姬</span>
                    </a>
                </div>
                <div class="bd">
                    <p class="">
                        导演: 陈凯歌&nbsp;&nbsp;&nbsp;
                        主演: 张国荣 / 张丰毅 / 巩俐
                    </p>
                    <p class="">
                        1993&nbsp;/&nbsp;中国大陆 香港&nbsp;/&nbsp;剧情 爱情
                    </p>
                </div>
                <div class="star">
                    <span class="rating5-t"></span>
                    <span class="rating_num" property="v:average">9.6</span>
                    <span property="v:best" content="10.0"></span>
                    <span>1847479人评价</span>
                </div>
                <p class="quote">
                    <span class="inq">风华绝代。</span>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
"""

# 创建BeautifulSoup对象
soup = BeautifulSoup(html_content, 'html.parser')

print("=== 基础解析示例 ===")

# 1. 查找页面标题
title = soup.find('title').text
print(f"页面标题: {title}")

# 2. 查找所有电影条目
movies = soup.find_all('div', class_='item')
print(f"找到 {len(movies)} 部电影")

# 3. 提取第一部电影的信息
first_movie = movies[0]
movie_title = first_movie.find('span', class_='title').text
movie_rating = first_movie.find('span', class_='rating_num').text
movie_quote = first_movie.find('span', class_='inq').text

print(f"\n第一部电影:")
print(f"标题: {movie_title}")
print(f"评分: {movie_rating}")
print(f"经典台词: {movie_quote}")
```

### 选择器方法详解
```python
# 文件：day2_selectors.py
from bs4 import BeautifulSoup

# 使用上面的html_content
soup = BeautifulSoup(html_content, 'html.parser')

print("=== 不同选择器方法对比 ===")

# 方法1: find() - 查找第一个匹配元素
first_title = soup.find('span', class_='title')
print(f"find方法: {first_title.text}")

# 方法2: find_all() - 查找所有匹配元素
all_titles = soup.find_all('span', class_='title')
print(f"find_all方法: 找到{len(all_titles)}个标题")

# 方法3: CSS选择器 - select()
css_titles = soup.select('.title')
print(f"CSS选择器: 找到{len(css_titles)}个标题")

# 方法4: CSS选择器 - select_one()
first_css_title = soup.select_one('.title')
print(f"select_one方法: {first_css_title.text}")

# 方法5: 属性选择器
data_id_items = soup.find_all('div', attrs={'data-id': True})
print(f"属性选择器: 找到{len(data_id_items)}个有data-id的元素")

# 方法6: 层级选择器
nested_titles = soup.select('.info .hd .title')
print(f"层级选择器: 找到{len(nested_titles)}个嵌套标题")

print("\n=== 常用CSS选择器语法 ===")
print("类选择器: .class-name")
print("ID选择器: #element-id")
print("标签选择器: tag-name")
print("属性选择器: [attribute='value']")
print("层级选择器: parent child")
print("直接子元素: parent > child")
```

---

## 🎯 第三步：实战练习 - 电影信息提取 (45分钟)

### 练习1: 完整电影信息提取 (20分钟)
```python
# 文件：day2_movie_extractor.py
from bs4 import BeautifulSoup
import re
import json
from datetime import datetime

def extract_movie_info(html_content):
    """提取电影信息的完整函数"""
    soup = BeautifulSoup(html_content, 'html.parser')
    movies = []
    
    # 查找所有电影条目
    movie_items = soup.find_all('div', class_='item')
    
    for item in movie_items:
        try:
            # 基础信息提取
            movie_data = {}
            
            # 1. 电影ID
            movie_data['id'] = item.get('data-id')
            
            # 2. 电影标题（处理中英文标题）
            title_elements = item.find_all('span', class_='title')
            if title_elements:
                chinese_title = title_elements[0].text.strip()
                english_title = ""
                if len(title_elements) > 1:
                    english_title = title_elements[1].text.strip().replace('/', '').replace('&nbsp;', '').strip()
                
                movie_data['chinese_title'] = chinese_title
                movie_data['english_title'] = english_title
            
            # 3. 评分信息
            rating_element = item.find('span', class_='rating_num')
            if rating_element:
                movie_data['rating'] = float(rating_element.text.strip())
            
            # 4. 评价人数
            rating_people = item.find('div', class_='star')
            if rating_people:
                people_text = rating_people.find_all('span')[-1].text
                # 提取数字
                people_match = re.search(r'(\d+)人评价', people_text)
                if people_match:
                    movie_data['rating_people'] = int(people_match.group(1))
            
            # 5. 导演和主演信息
            bd_element = item.find('div', class_='bd')
            if bd_element:
                p_elements = bd_element.find_all('p')
                if p_elements:
                    # 第一个p标签包含导演和主演信息
                    director_actor_text = p_elements[0].text.strip()
                    
                    # 提取导演
                    director_match = re.search(r'导演:\s*([^&]+)', director_actor_text)
                    if director_match:
                        movie_data['director'] = director_match.group(1).strip()
                    
                    # 提取主演
                    actor_match = re.search(r'主演:\s*([^&]+)', director_actor_text)
                    if actor_match:
                        movie_data['actors'] = actor_match.group(1).strip()
                    
                    # 第二个p标签包含年份、地区、类型
                    if len(p_elements) > 1:
                        year_region_genre = p_elements[1].text.strip()
                        parts = [part.strip() for part in year_region_genre.split('/')]
                        
                        if len(parts) >= 3:
                            movie_data['year'] = parts[0].strip()
                            movie_data['region'] = parts[1].strip()
                            movie_data['genre'] = parts[2].strip()
            
            # 6. 经典台词
            quote_element = item.find('span', class_='inq')
            if quote_element:
                movie_data['quote'] = quote_element.text.strip()
            
            # 7. 电影链接
            link_element = item.find('a')
            if link_element:
                movie_data['link'] = link_element.get('href')
            
            # 8. 添加提取时间
            movie_data['extracted_at'] = datetime.now().isoformat()
            
            movies.append(movie_data)
            
        except Exception as e:
            print(f"提取电影信息时出错: {e}")
            continue
    
    return movies

# 使用函数提取电影信息
movies_data = extract_movie_info(html_content)

# 打印结果
print("=== 提取的电影信息 ===")
for i, movie in enumerate(movies_data, 1):
    print(f"\n电影 {i}:")
    for key, value in movie.items():
        print(f"  {key}: {value}")

# 保存为JSON文件
with open('movies_data.json', 'w', encoding='utf-8') as f:
    json.dump(movies_data, f, ensure_ascii=False, indent=2)

print(f"\n成功提取 {len(movies_data)} 部电影信息，已保存到 movies_data.json")
```

### 练习2: 数据清洗和验证 (15分钟)
```python
# 文件：day2_data_cleaning.py
import re
import json
from typing import List, Dict, Any

def clean_movie_data(movies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """清洗和验证电影数据"""
    cleaned_movies = []
    
    for movie in movies:
        cleaned_movie = {}
        
        # 1. 清洗标题
        if 'chinese_title' in movie:
            title = movie['chinese_title'].strip()
            # 移除特殊字符
            title = re.sub(r'[^\w\s\u4e00-\u9fff]', '', title)
            cleaned_movie['title'] = title
        
        # 2. 验证评分
        if 'rating' in movie:
            rating = movie['rating']
            if isinstance(rating, (int, float)) and 0 <= rating <= 10:
                cleaned_movie['rating'] = round(float(rating), 1)
        
        # 3. 清洗年份
        if 'year' in movie:
            year_text = movie['year']
            year_match = re.search(r'(\d{4})', year_text)
            if year_match:
                year = int(year_match.group(1))
                if 1900 <= year <= 2030:  # 合理的年份范围
                    cleaned_movie['year'] = year
        
        # 4. 清洗导演信息
        if 'director' in movie:
            director = movie['director'].strip()
            # 移除多余的空白字符
            director = re.sub(r'\s+', ' ', director)
            cleaned_movie['director'] = director
        
        # 5. 处理评价人数
        if 'rating_people' in movie:
            people = movie['rating_people']
            if isinstance(people, int) and people > 0:
                cleaned_movie['rating_people'] = people
        
        # 6. 清洗类型信息
        if 'genre' in movie:
            genre = movie['genre'].strip()
            # 分割多个类型
            genres = [g.strip() for g in re.split(r'[/\s]+', genre) if g.strip()]
            cleaned_movie['genres'] = genres
        
        # 7. 验证必需字段
        required_fields = ['title', 'rating']
        if all(field in cleaned_movie for field in required_fields):
            cleaned_movies.append(cleaned_movie)
    
    return cleaned_movies

def validate_movie_data(movies: List[Dict[str, Any]]) -> Dict[str, Any]:
    """验证数据质量"""
    stats = {
        'total_movies': len(movies),
        'movies_with_rating': 0,
        'movies_with_year': 0,
        'movies_with_director': 0,
        'average_rating': 0,
        'rating_distribution': {'9+': 0, '8-9': 0, '7-8': 0, '<7': 0}
    }
    
    total_rating = 0
    rating_count = 0
    
    for movie in movies:
        if 'rating' in movie:
            stats['movies_with_rating'] += 1
            rating = movie['rating']
            total_rating += rating
            rating_count += 1
            
            # 评分分布
            if rating >= 9:
                stats['rating_distribution']['9+'] += 1
            elif rating >= 8:
                stats['rating_distribution']['8-9'] += 1
            elif rating >= 7:
                stats['rating_distribution']['7-8'] += 1
            else:
                stats['rating_distribution']['<7'] += 1
        
        if 'year' in movie:
            stats['movies_with_year'] += 1
        
        if 'director' in movie:
            stats['movies_with_director'] += 1
    
    if rating_count > 0:
        stats['average_rating'] = round(total_rating / rating_count, 2)
    
    return stats

# 加载之前提取的数据
try:
    with open('movies_data.json', 'r', encoding='utf-8') as f:
        raw_movies = json.load(f)
    
    # 清洗数据
    cleaned_movies = clean_movie_data(raw_movies)
    
    # 验证数据质量
    stats = validate_movie_data(cleaned_movies)
    
    print("=== 数据清洗结果 ===")
    print(f"原始数据: {len(raw_movies)} 条")
    print(f"清洗后数据: {len(cleaned_movies)} 条")
    
    print("\n=== 数据质量统计 ===")
    for key, value in stats.items():
        print(f"{key}: {value}")
    
    # 保存清洗后的数据
    with open('cleaned_movies.json', 'w', encoding='utf-8') as f:
        json.dump(cleaned_movies, f, ensure_ascii=False, indent=2)
    
    print("\n清洗后的数据已保存到 cleaned_movies.json")
    
except FileNotFoundError:
    print("请先运行 day2_movie_extractor.py 生成原始数据")
```

### 练习3: 错误处理和异常情况 (10分钟)
```python
# 文件：day2_error_handling.py
from bs4 import BeautifulSoup
import requests
from typing import Optional, Dict, Any
import time

def safe_extract_text(element, default: str = "") -> str:
    """安全提取元素文本"""
    try:
        return element.text.strip() if element else default
    except AttributeError:
        return default

def safe_extract_attribute(element, attr: str, default: str = "") -> str:
    """安全提取元素属性"""
    try:
        return element.get(attr, default) if element else default
    except AttributeError:
        return default

def robust_movie_extractor(html_content: str) -> List[Dict[str, Any]]:
    """健壮的电影信息提取器"""
    movies = []

    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        movie_items = soup.find_all('div', class_='item')

        for i, item in enumerate(movie_items):
            movie = {}

            try:
                # 使用安全提取函数
                movie['id'] = safe_extract_attribute(item, 'data-id', f'unknown_{i}')

                # 标题提取（处理可能的缺失情况）
                title_element = item.find('span', class_='title')
                movie['title'] = safe_extract_text(title_element, f'未知电影_{i}')

                # 评分提取（处理非数字情况）
                rating_element = item.find('span', class_='rating_num')
                rating_text = safe_extract_text(rating_element)
                try:
                    movie['rating'] = float(rating_text) if rating_text else 0.0
                except ValueError:
                    movie['rating'] = 0.0

                # 导演信息提取（处理复杂文本）
                bd_element = item.find('div', class_='bd')
                if bd_element:
                    p_elements = bd_element.find_all('p')
                    if p_elements:
                        director_text = safe_extract_text(p_elements[0])
                        # 使用正则表达式安全提取导演
                        import re
                        director_match = re.search(r'导演:\s*([^&\n]+)', director_text)
                        movie['director'] = director_match.group(1).strip() if director_match else "未知导演"

                # 经典台词（可能不存在）
                quote_element = item.find('span', class_='inq')
                movie['quote'] = safe_extract_text(quote_element, "暂无经典台词")

                movies.append(movie)

            except Exception as e:
                print(f"处理第 {i+1} 部电影时出错: {e}")
                # 添加错误记录但继续处理
                movies.append({
                    'id': f'error_{i}',
                    'title': f'解析错误_{i}',
                    'error': str(e)
                })
                continue

    except Exception as e:
        print(f"HTML解析失败: {e}")
        return []

    return movies

# 测试错误处理
print("=== 测试错误处理能力 ===")

# 测试1: 正常HTML
normal_result = robust_movie_extractor(html_content)
print(f"正常HTML解析结果: {len(normal_result)} 部电影")

# 测试2: 损坏的HTML
broken_html = """
<div class="item">
    <span class="title">测试电影</span>
    <span class="rating_num">abc</span>  <!-- 非数字评分 -->
</div>
<div class="item">
    <!-- 缺少标题元素 -->
    <span class="rating_num">8.5</span>
</div>
"""

broken_result = robust_movie_extractor(broken_html)
print(f"损坏HTML解析结果: {len(broken_result)} 部电影")
for movie in broken_result:
    print(f"  - {movie}")

# 测试3: 空HTML
empty_result = robust_movie_extractor("")
print(f"空HTML解析结果: {len(empty_result)} 部电影")
```

---

## 🔍 第四步：正则表达式实战 (30分钟)

### 正则表达式基础
```python
# 文件：day2_regex_basics.py
import re

print("=== 正则表达式基础教程 ===")

# 示例文本
text = """
电影信息：
《肖申克的救赎》 评分：9.7分 年份：1994年
导演：弗兰克·德拉邦特 主演：蒂姆·罗宾斯、摩根·弗里曼
联系方式：
邮箱：<EMAIL>, <EMAIL>
电话：010-12345678, 400-800-8888
网址：https://movie.douban.com, http://www.imdb.com
时间：2024-08-01 14:30:25
"""

# 1. 基础匹配
print("1. 基础匹配:")
movie_titles = re.findall(r'《([^》]+)》', text)
print(f"电影标题: {movie_titles}")

# 2. 数字匹配
print("\n2. 数字匹配:")
ratings = re.findall(r'(\d+\.\d+)分', text)
print(f"评分: {ratings}")

years = re.findall(r'(\d{4})年', text)
print(f"年份: {years}")

# 3. 邮箱匹配
print("\n3. 邮箱匹配:")
emails = re.findall(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b', text)
print(f"邮箱地址: {emails}")

# 4. 电话号码匹配
print("\n4. 电话号码匹配:")
phones = re.findall(r'\b(?:\d{3}-\d{8}|\d{3}-\d{3}-\d{4})\b', text)
print(f"电话号码: {phones}")

# 5. URL匹配
print("\n5. URL匹配:")
urls = re.findall(r'https?://[^\s,]+', text)
print(f"网址: {urls}")

# 6. 日期时间匹配
print("\n6. 日期时间匹配:")
datetime_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
datetimes = re.findall(datetime_pattern, text)
print(f"日期时间: {datetimes}")

# 7. 中文姓名匹配
print("\n7. 中文姓名匹配:")
chinese_names = re.findall(r'[\u4e00-\u9fff]{2,4}·?[\u4e00-\u9fff]{0,3}', text)
print(f"中文姓名: {chinese_names}")
```

### 高级正则表达式应用
```python
# 文件：day2_regex_advanced.py
import re
from typing import Dict, List

def extract_movie_details_with_regex(text: str) -> Dict[str, any]:
    """使用正则表达式提取电影详细信息"""

    movie_info = {}

    # 1. 提取电影标题（支持中英文）
    title_patterns = [
        r'《([^》]+)》',  # 中文书名号
        r'"([^"]+)"',    # 英文引号
        r'【([^】]+)】'   # 中文方括号
    ]

    for pattern in title_patterns:
        titles = re.findall(pattern, text)
        if titles:
            movie_info['titles'] = titles
            break

    # 2. 提取评分信息
    rating_patterns = [
        r'评分[：:]\s*(\d+\.?\d*)分?',
        r'(\d+\.?\d+)\s*分',
        r'rating[：:]\s*(\d+\.?\d*)'
    ]

    for pattern in rating_patterns:
        ratings = re.findall(pattern, text, re.IGNORECASE)
        if ratings:
            movie_info['rating'] = float(ratings[0])
            break

    # 3. 提取年份
    year_patterns = [
        r'(\d{4})年',
        r'年份[：:]\s*(\d{4})',
        r'(\d{4})\s*年'
    ]

    for pattern in year_patterns:
        years = re.findall(pattern, text)
        if years:
            movie_info['year'] = int(years[0])
            break

    # 4. 提取导演信息
    director_patterns = [
        r'导演[：:]\s*([^\n\r]+?)(?:\s+主演|$)',
        r'Director[：:]\s*([^\n\r]+?)(?:\s+|$)',
        r'执导[：:]\s*([^\n\r]+?)(?:\s+|$)'
    ]

    for pattern in director_patterns:
        directors = re.findall(pattern, text, re.IGNORECASE)
        if directors:
            # 清理导演名字
            director = directors[0].strip()
            director = re.sub(r'[，,、]', '/', director)  # 统一分隔符
            movie_info['director'] = director
            break

    # 5. 提取主演信息
    actor_patterns = [
        r'主演[：:]\s*([^\n\r]+?)(?:\s+|$)',
        r'演员[：:]\s*([^\n\r]+?)(?:\s+|$)',
        r'Starring[：:]\s*([^\n\r]+?)(?:\s+|$)'
    ]

    for pattern in actor_patterns:
        actors = re.findall(pattern, text, re.IGNORECASE)
        if actors:
            actor = actors[0].strip()
            actor = re.sub(r'[，,、]', '/', actor)  # 统一分隔符
            movie_info['actors'] = actor
            break

    # 6. 提取类型信息
    genre_patterns = [
        r'类型[：:]\s*([^\n\r]+?)(?:\s+|$)',
        r'Genre[：:]\s*([^\n\r]+?)(?:\s+|$)',
        r'([^\s]+)\s*类型'
    ]

    for pattern in genre_patterns:
        genres = re.findall(pattern, text, re.IGNORECASE)
        if genres:
            genre = genres[0].strip()
            genre_list = re.split(r'[，,、/\s]+', genre)
            movie_info['genres'] = [g.strip() for g in genre_list if g.strip()]
            break

    return movie_info

# 测试正则表达式提取
test_texts = [
    """
    《肖申克的救赎》是一部经典电影
    评分：9.7分
    年份：1994年
    导演：弗兰克·德拉邦特
    主演：蒂姆·罗宾斯、摩根·弗里曼
    类型：剧情、犯罪
    """,

    """
    "The Shawshank Redemption"
    Rating: 9.7
    Director: Frank Darabont
    Starring: Tim Robbins, Morgan Freeman
    Genre: Drama, Crime
    """,

    """
    【霸王别姬】
    评分: 9.6分
    1993年上映
    陈凯歌执导
    演员: 张国荣/张丰毅/巩俐
    剧情 爱情类型
    """
]

print("=== 正则表达式提取测试 ===")
for i, text in enumerate(test_texts, 1):
    print(f"\n测试文本 {i}:")
    result = extract_movie_details_with_regex(text)
    for key, value in result.items():
        print(f"  {key}: {value}")
```

---

## 🌐 第五步：网络请求与实际爬虫 (30分钟)

### 安全的网络请求
```python
# 文件：day2_web_scraping.py
import requests
from bs4 import BeautifulSoup
import time
import random
from typing import Optional, Dict, List
import json

class MovieScraper:
    """电影信息爬虫类"""

    def __init__(self):
        self.session = requests.Session()
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        self.session.headers.update(self.headers)

    def get_page(self, url: str, max_retries: int = 3) -> Optional[str]:
        """安全获取网页内容"""
        for attempt in range(max_retries):
            try:
                # 随机延迟，避免请求过快
                time.sleep(random.uniform(1, 3))

                response = self.session.get(url, timeout=10)
                response.raise_for_status()  # 检查HTTP错误

                # 检查内容类型
                content_type = response.headers.get('content-type', '')
                if 'text/html' not in content_type:
                    print(f"警告: 非HTML内容类型: {content_type}")

                return response.text

            except requests.exceptions.RequestException as e:
                print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    return None
                time.sleep(random.uniform(2, 5))  # 失败后等待更长时间

        return None

    def parse_movie_list(self, html: str) -> List[Dict]:
        """解析电影列表页面"""
        if not html:
            return []

        soup = BeautifulSoup(html, 'html.parser')
        movies = []

        # 这里使用模拟的HTML结构，实际使用时需要根据目标网站调整
        movie_items = soup.find_all('div', class_='item')

        for item in movie_items:
            try:
                movie = self.extract_movie_from_item(item)
                if movie:
                    movies.append(movie)
            except Exception as e:
                print(f"解析电影条目时出错: {e}")
                continue

        return movies

    def extract_movie_from_item(self, item) -> Optional[Dict]:
        """从单个电影条目提取信息"""
        movie = {}

        # 标题
        title_elem = item.find('span', class_='title')
        if title_elem:
            movie['title'] = title_elem.text.strip()
        else:
            return None  # 没有标题的条目无效

        # 评分
        rating_elem = item.find('span', class_='rating_num')
        if rating_elem:
            try:
                movie['rating'] = float(rating_elem.text.strip())
            except ValueError:
                movie['rating'] = 0.0

        # 链接
        link_elem = item.find('a')
        if link_elem:
            movie['link'] = link_elem.get('href', '')

        # 添加爬取时间
        movie['scraped_at'] = time.strftime('%Y-%m-%d %H:%M:%S')

        return movie

    def scrape_movies(self, base_url: str, pages: int = 1) -> List[Dict]:
        """爬取多页电影信息"""
        all_movies = []

        for page in range(pages):
            print(f"正在爬取第 {page + 1} 页...")

            # 构造页面URL（根据实际网站调整）
            if page == 0:
                url = base_url
            else:
                url = f"{base_url}?start={page * 25}"

            html = self.get_page(url)
            if html:
                movies = self.parse_movie_list(html)
                all_movies.extend(movies)
                print(f"第 {page + 1} 页获取到 {len(movies)} 部电影")
            else:
                print(f"第 {page + 1} 页获取失败")
                break

        return all_movies

    def save_movies(self, movies: List[Dict], filename: str):
        """保存电影数据"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(movies, f, ensure_ascii=False, indent=2)
        print(f"已保存 {len(movies)} 部电影信息到 {filename}")

# 使用示例（注意：这里使用模拟数据，实际使用时需要遵守网站的robots.txt）
def demo_scraping():
    """演示爬虫使用"""
    scraper = MovieScraper()

    # 注意：这里使用模拟的URL和数据
    # 实际使用时需要：
    # 1. 检查网站的robots.txt
    # 2. 遵守网站的使用条款
    # 3. 控制请求频率
    # 4. 处理反爬虫机制

    print("=== 电影爬虫演示 ===")
    print("注意：这是演示代码，实际使用时请遵守网站规则")

    # 模拟爬取过程
    mock_html = html_content  # 使用之前定义的HTML内容
    movies = scraper.parse_movie_list(mock_html)

    if movies:
        scraper.save_movies(movies, 'scraped_movies.json')

        # 显示统计信息
        print(f"\n=== 爬取统计 ===")
        print(f"总计电影数量: {len(movies)}")

        if movies:
            ratings = [m['rating'] for m in movies if 'rating' in m and m['rating'] > 0]
            if ratings:
                avg_rating = sum(ratings) / len(ratings)
                print(f"平均评分: {avg_rating:.2f}")
                print(f"最高评分: {max(ratings)}")
                print(f"最低评分: {min(ratings)}")

# 运行演示
if __name__ == "__main__":
    demo_scraping()
```

---

## 🔗 第六步：与FastAPI集成 (20分钟)

### 创建爬虫API接口
```python
# 文件：day2_scraper_api.py
from fastapi import FastAPI, BackgroundTasks, HTTPException
from pydantic import BaseModel
from typing import List, Optional
import json
import os
from datetime import datetime

# 重用之前的爬虫类
from day2_web_scraping import MovieScraper

app = FastAPI(title="电影爬虫API", version="1.0.0")

class ScrapeRequest(BaseModel):
    """爬取请求模型"""
    url: str
    pages: int = 1
    delay: float = 2.0

class MovieResponse(BaseModel):
    """电影响应模型"""
    title: str
    rating: Optional[float] = None
    link: Optional[str] = None
    scraped_at: str

class ScrapeResult(BaseModel):
    """爬取结果模型"""
    success: bool
    message: str
    total_movies: int
    movies: List[MovieResponse]
    scraped_at: str

# 全局爬虫实例
scraper = MovieScraper()

@app.post("/scrape", response_model=ScrapeResult)
async def scrape_movies(request: ScrapeRequest):
    """爬取电影信息接口"""
    try:
        # 这里使用模拟数据，实际项目中会真正爬取
        print(f"模拟爬取: {request.url}, 页数: {request.pages}")

        # 使用之前的HTML内容进行演示
        from day2_bs4_intro import html_content
        movies = scraper.parse_movie_list(html_content)

        # 转换为响应模型
        movie_responses = []
        for movie in movies:
            movie_response = MovieResponse(
                title=movie.get('title', ''),
                rating=movie.get('rating'),
                link=movie.get('link'),
                scraped_at=movie.get('scraped_at', datetime.now().isoformat())
            )
            movie_responses.append(movie_response)

        return ScrapeResult(
            success=True,
            message=f"成功爬取 {len(movie_responses)} 部电影",
            total_movies=len(movie_responses),
            movies=movie_responses,
            scraped_at=datetime.now().isoformat()
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"爬取失败: {str(e)}")

@app.get("/movies", response_model=List[MovieResponse])
async def get_scraped_movies():
    """获取已爬取的电影列表"""
    try:
        # 从文件读取已爬取的数据
        if os.path.exists('scraped_movies.json'):
            with open('scraped_movies.json', 'r', encoding='utf-8') as f:
                movies_data = json.load(f)

            movies = []
            for movie in movies_data:
                movie_response = MovieResponse(
                    title=movie.get('title', ''),
                    rating=movie.get('rating'),
                    link=movie.get('link'),
                    scraped_at=movie.get('scraped_at', '')
                )
                movies.append(movie_response)

            return movies
        else:
            return []

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"读取数据失败: {str(e)}")

@app.get("/stats")
async def get_scrape_stats():
    """获取爬取统计信息"""
    try:
        if os.path.exists('scraped_movies.json'):
            with open('scraped_movies.json', 'r', encoding='utf-8') as f:
                movies = json.load(f)

            total_movies = len(movies)
            ratings = [m['rating'] for m in movies if 'rating' in m and m['rating'] and m['rating'] > 0]

            stats = {
                "total_movies": total_movies,
                "movies_with_rating": len(ratings),
                "average_rating": round(sum(ratings) / len(ratings), 2) if ratings else 0,
                "highest_rating": max(ratings) if ratings else 0,
                "lowest_rating": min(ratings) if ratings else 0,
                "last_updated": datetime.now().isoformat()
            }

            return stats
        else:
            return {"message": "暂无爬取数据"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"统计失败: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
```

---

## ✅ 下午学习检查清单

### HTML解析基础 (必须掌握)
- [ ] 理解HTML文档结构
- [ ] 掌握BeautifulSoup基本用法
- [ ] 熟练使用CSS选择器
- [ ] 能处理不同的HTML元素类型
- [ ] 理解find()和find_all()的区别

### 数据提取技能 (必须会做)
- [ ] 能提取文本内容和属性值
- [ ] 能处理嵌套的HTML结构
- [ ] 能使用正则表达式清洗数据
- [ ] 能处理缺失数据和异常情况
- [ ] 能验证和清洗提取的数据

### 正则表达式应用 (重要技能)
- [ ] 掌握基本正则表达式语法
- [ ] 能提取邮箱、电话、URL等格式化数据
- [ ] 能处理中文文本匹配
- [ ] 能使用分组和替换功能
- [ ] 能结合BeautifulSoup使用正则表达式

### 网络爬虫实践 (进阶技能)
- [ ] 理解HTTP请求和响应
- [ ] 能设置合适的请求头
- [ ] 能处理网络异常和重试
- [ ] 了解爬虫礼仪和法律规范
- [ ] 能将爬虫集成到API中

---

## 🚀 下午总结与晚上预告

### 🎯 下午成果
完成了HTML解析和数据提取的核心技能：
1. ✅ 掌握了BeautifulSoup的各种选择器
2. ✅ 学会了正则表达式数据提取
3. ✅ 实现了健壮的错误处理机制
4. ✅ 创建了完整的爬虫系统
5. ✅ 将爬虫功能集成到API中

### 🌙 晚上整合预告
晚上将进行综合实战：
- 将上午的FastAPI与下午的爬虫结合
- 实现完整的视频平台数据收集系统
- 添加数据库存储功能
- 创建前端展示页面

### 💡 实践建议
1. **立即实践** (20分钟)：
   - 运行所有的解析示例
   - 尝试修改选择器和正则表达式
   - 测试错误处理功能

2. **扩展练习** (20分钟)：
   - 尝试解析其他网站结构
   - 添加更多数据字段提取
   - 实现数据去重功能

3. **准备晚上整合**：
   - 确保上午的FastAPI服务正常运行
   - 确保下午的爬虫代码能正常执行
   - 准备数据库连接（SQLite或其他）

### ⚠️ 重要提醒
1. **遵守法律法规**：
   - 检查网站的robots.txt文件
   - 遵守网站的使用条款
   - 不要过于频繁地请求

2. **技术最佳实践**：
   - 始终添加适当的延迟
   - 使用合适的User-Agent
   - 处理所有可能的异常情况

**记住**：爬虫技术是工具，关键在于合法合理地使用！
```
```
