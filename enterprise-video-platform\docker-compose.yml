version: '3.8'

services:
  # 主应用服务
  app:
    build: .
    container_name: video-platform-app
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/video_platform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./downloads:/app/downloads
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - video-platform-network

  # PostgreSQL数据库
  db:
    image: postgres:15-alpine
    container_name: video-platform-db
    environment:
      - POSTGRES_DB=video_platform
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - video-platform-network

  # Redis缓存和消息队列
  redis:
    image: redis:7-alpine
    container_name: video-platform-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - video-platform-network

  # Celery Worker
  celery-worker:
    build: .
    container_name: video-platform-worker
    command: celery -A app.services.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=**************************************/video_platform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./downloads:/app/downloads
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - video-platform-network

  # Celery Beat (定时任务)
  celery-beat:
    build: .
    container_name: video-platform-beat
    command: celery -A app.services.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=**************************************/video_platform
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    volumes:
      - ./logs:/app/logs
    depends_on:
      - db
      - redis
    restart: unless-stopped
    networks:
      - video-platform-network

  # Nginx反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: video-platform-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - video-platform-network

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: video-platform-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
    restart: unless-stopped
    networks:
      - video-platform-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:

networks:
  video-platform-network:
    driver: bridge
