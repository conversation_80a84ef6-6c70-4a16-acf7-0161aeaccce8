# 🎬 腾讯视频爬虫框架 - 完整使用指南

## 📁 项目文件结构

```
TXvideo/
├── 📚 文档文件
│   ├── README.md              # 详细项目说明
│   ├── FINAL_SUMMARY.md       # 项目总结
│   └── 使用指南.md            # 本文件
│
├── 🎯 核心文件
│   ├── tx_video_spider.py     # 主爬虫类（核心）
│   ├── config.py              # 配置文件
│   └── utils.py               # 工具函数
│
├── 📖 示例和测试
│   ├── example.py             # 交互式使用示例
│   ├── test_spider.py         # 基础功能测试
│   ├── test_free_video.py     # 免费视频测试
│   ├── debug_test.py          # 调试测试
│   ├── advanced_test.py       # 高级测试
│   └── find_free_video.py     # 寻找免费视频
│
├── 📦 配置文件
│   └── requirements.txt       # 依赖包列表
│
└── 📂 输出目录
    └── downloads/             # 下载的视频存放目录
```

## 🚀 快速开始

### 1. 安装依赖
```bash
cd TXvideo
pip install -r requirements.txt
```

### 2. 运行交互式示例
```bash
python example.py
```

### 3. 基本使用代码
```python
from tx_video_spider import TencentVideoSpider

# 创建爬虫实例
spider = TencentVideoSpider()

# 爬取视频
video_url = "https://v.qq.com/x/cover/xxx/yyy.html"
success = spider.crawl_video(video_url, "my_video.mp4")

if success:
    print("✅ 下载成功！")
else:
    print("❌ 下载失败！")
```

## 📋 详细使用方法

### 方法1：交互式使用（推荐新手）
```bash
python example.py
```
然后按提示选择功能：
- `1` - 单个视频下载
- `2` - 批量视频下载  
- `3` - 仅提取视频信息
- `4` - 自定义配置示例

### 方法2：直接调用API
```python
from tx_video_spider import TencentVideoSpider

spider = TencentVideoSpider()

# 步骤1：提取视频信息
video_info = spider.extract_video_info(video_url)
print(f"VID: {video_info['vid']}")
print(f"Cover ID: {video_info['coverid']}")

# 步骤2：生成请求参数
params = spider.generate_params(video_info['vid'], video_info['coverid'])

# 步骤3：获取视频数据
video_data = spider.get_video_info(params)

# 步骤4：解析视频URL
video_urls = spider.parse_video_urls(video_data)

# 步骤5：下载视频
if video_urls:
    success = spider.download_video(video_urls[0], "output.mp4")
```

### 方法3：批量处理
```python
from tx_video_spider import TencentVideoSpider

spider = TencentVideoSpider()

video_urls = [
    "https://v.qq.com/x/cover/xxx1/yyy1.html",
    "https://v.qq.com/x/cover/xxx2/yyy2.html",
    # 更多URL...
]

for i, url in enumerate(video_urls, 1):
    output_file = f"video_{i:03d}.mp4"
    success = spider.crawl_video(url, output_file)
    print(f"视频 {i}: {'成功' if success else '失败'}")
```

## 🧪 测试和调试

### 基础功能测试
```bash
python test_spider.py
```
检查所有依赖和基础功能是否正常。

### 免费视频测试
```bash
python test_free_video.py
```
测试特定的免费视频链接。

### 高级调试测试
```bash
python advanced_test.py
```
尝试多种策略解决访问问题。

### 寻找可用视频
```bash
python find_free_video.py
```
自动从腾讯视频首页寻找可能免费的视频。

## ⚙️ 配置说明

### 修改下载目录
编辑 `config.py`：
```python
DOWNLOAD_CONFIG = {
    'OUTPUT_DIR': './my_downloads/',  # 修改下载目录
    'TIMEOUT': 60,                    # 修改超时时间
    'RETRY_TIMES': 5,                 # 修改重试次数
}
```

### 使用代理
```python
spider = TencentVideoSpider()
spider.session.proxies = {
    'http': 'http://proxy.example.com:8080',
    'https': 'https://proxy.example.com:8080'
}
```

### 修改请求头
```python
spider = TencentVideoSpider()
spider.session.headers.update({
    'User-Agent': '自定义User-Agent',
    'Custom-Header': '自定义值'
})
```

## 🔍 支持的URL格式

框架支持以下腾讯视频URL格式：

1. **标准格式**：
   ```
   https://v.qq.com/x/cover/coverid/vid.html
   ```

2. **单ID格式**：
   ```
   https://v.qq.com/x/cover/single_id.html
   ```

3. **带参数格式**：
   ```
   https://v.qq.com/x/cover/xxx/yyy.html?param=value
   ```

4. **查询参数格式**：
   ```
   https://v.qq.com/x/page/xxx.html?vid=yyy&coverid=zzz
   ```

## 🛠️ 常见问题解决

### Q1: 提示"视频被外星人劫走"
**原因**：视频访问受限（VIP、地区限制等）
**解决**：
1. 尝试免费的预告片或新闻视频
2. 检查视频是否真的免费
3. 使用代理服务器

### Q2: 无法提取vid
**原因**：URL格式不支持
**解决**：
1. 检查URL格式是否正确
2. 尝试从浏览器复制完整URL
3. 使用页面内容提取功能

### Q3: 下载速度慢
**解决**：
1. 调整并发数量
2. 使用代理服务器
3. 选择较低的视频质量

### Q4: 依赖安装失败
**解决**：
```bash
# 升级pip
pip install --upgrade pip

# 单独安装问题包
pip install PyExecJS
pip install pycryptodome
pip install requests
```

## 📊 错误代码含义

| 错误代码 | 含义 | 解决方案 |
|---------|------|----------|
| 61.1 | 视频不存在或已下架 | 检查URL，尝试其他视频 |
| 85.-12 | 视频访问受限 | 尝试免费视频或使用代理 |
| 10001 | 参数错误 | 检查cKey生成算法 |
| 30002 | 需要VIP权限 | 寻找免费视频 |
| 40001 | 地区限制 | 使用VPN或代理 |

## 🎯 最佳实践

### 1. 寻找合适的测试视频
- ✅ 免费电影预告片
- ✅ 新闻短视频
- ✅ 公开宣传片
- ❌ VIP专享内容
- ❌ 地区限制内容

### 2. 调试流程
1. 在浏览器中确认视频可以正常播放
2. 使用F12工具分析网络请求
3. 对比框架生成的参数与实际请求
4. 根据差异调整代码

### 3. 合规使用
- ⚠️ 仅用于学习研究
- ⚠️ 遵守相关法律法规
- ⚠️ 不要大规模爬取
- ⚠️ 尊重版权

## 📞 技术支持

如果遇到问题：

1. **查看日志**：检查 `tx_spider.log` 文件
2. **运行测试**：使用提供的测试脚本诊断问题
3. **调试模式**：使用 `debug_test.py` 获取详细信息
4. **参数对比**：使用F12工具对比实际请求

## 🎉 总结

这个框架已经具备了完整的腾讯视频爬取功能：
- ✅ 复杂的cKey加密算法
- ✅ 多种URL格式支持
- ✅ 完善的错误处理
- ✅ 详细的测试工具
- ✅ 专业的代码结构

**只需要找到合适的免费视频进行测试，即可验证完整的工作流程！**

---

**🚀 开始使用：运行 `python example.py` 体验完整功能！**
