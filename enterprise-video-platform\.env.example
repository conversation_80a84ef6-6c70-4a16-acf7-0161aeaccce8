# 应用配置
APP_NAME=企业级视频平台
APP_VERSION=1.0.0
DEBUG=true
SECRET_KEY=your-secret-key-here-change-in-production

# 服务器配置
HOST=0.0.0.0
PORT=8000

# 数据库配置
DATABASE_URL=sqlite:///./video_platform.db
# DATABASE_URL=postgresql://user:password@localhost:5432/video_platform

# Redis配置
REDIS_URL=redis://localhost:6379/0

# JWT配置
JWT_SECRET_KEY=your-jwt-secret-key-here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 爬虫配置
SPIDER_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36
SPIDER_TIMEOUT=30
SPIDER_RETRY_TIMES=3
SPIDER_DOWNLOAD_PATH=./downloads

# 文件存储配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=100MB

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=./logs/app.log

# 外部服务配置
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090

# 开发配置
RELOAD=true
WORKERS=1
