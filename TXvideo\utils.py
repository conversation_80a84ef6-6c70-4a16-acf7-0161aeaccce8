#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫工具类
"""

import os
import re
import time
import json
import hashlib
import logging
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, parse_qs
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class VideoURLParser:
    """视频URL解析器"""
    
    @staticmethod
    def extract_vid_coverid(url: str) -> Tuple[str, str]:
        """从URL中提取vid和coverid"""
        vid, coverid = "", ""
        
        try:
            # 方式1: 从路径中提取 /x/cover/coverid/vid.html
            if '/x/cover/' in url:
                match = re.search(r'/x/cover/([^/]+)/([^/\.]+)', url)
                if match:
                    coverid = match.group(1)
                    vid = match.group(2)
                    return vid, coverid
            
            # 方式2: 从查询参数中提取
            parsed_url = urlparse(url)
            query_params = parse_qs(parsed_url.query)
            vid = query_params.get('vid', [''])[0]
            coverid = query_params.get('coverid', [vid])[0]  # 如果没有coverid，使用vid
            
            # 方式3: 从页面内容中提取（需要请求页面）
            if not vid:
                vid, coverid = VideoURLParser._extract_from_page(url)
                
        except Exception as e:
            logging.error(f"解析URL失败: {e}")
            
        return vid, coverid
    
    @staticmethod
    def _extract_from_page(url: str) -> Tuple[str, str]:
        """从页面内容中提取vid和coverid"""
        try:
            response = requests.get(url, timeout=10)
            content = response.text
            
            # 查找vid
            vid_match = re.search(r'"vid"\s*:\s*"([^"]+)"', content)
            vid = vid_match.group(1) if vid_match else ""
            
            # 查找coverid
            coverid_match = re.search(r'"cover_id"\s*:\s*"([^"]+)"', content)
            coverid = coverid_match.group(1) if coverid_match else vid
            
            return vid, coverid
            
        except Exception as e:
            logging.error(f"从页面提取信息失败: {e}")
            return "", ""


class RequestManager:
    """请求管理器"""
    
    def __init__(self, headers: Dict = None, proxies: Dict = None):
        self.session = requests.Session()
        
        # 设置请求头
        if headers:
            self.session.headers.update(headers)
        
        # 设置代理
        if proxies:
            self.session.proxies.update(proxies)
        
        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def get(self, url: str, **kwargs) -> requests.Response:
        """GET请求"""
        return self.session.get(url, **kwargs)
    
    def post(self, url: str, **kwargs) -> requests.Response:
        """POST请求"""
        return self.session.post(url, **kwargs)


class FileManager:
    """文件管理器"""
    
    @staticmethod
    def ensure_dir(directory: str) -> None:
        """确保目录存在"""
        if not os.path.exists(directory):
            os.makedirs(directory)
    
    @staticmethod
    def get_safe_filename(filename: str) -> str:
        """获取安全的文件名"""
        # 移除或替换不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            filename = filename.replace(char, '_')
        return filename
    
    @staticmethod
    def get_file_size(filepath: str) -> int:
        """获取文件大小"""
        try:
            return os.path.getsize(filepath)
        except OSError:
            return 0
    
    @staticmethod
    def calculate_md5(filepath: str) -> str:
        """计算文件MD5"""
        hash_md5 = hashlib.md5()
        try:
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""


class ProgressBar:
    """进度条显示器"""
    
    def __init__(self, total: int, desc: str = "Progress"):
        self.total = total
        self.current = 0
        self.desc = desc
        self.start_time = time.time()
    
    def update(self, n: int = 1) -> None:
        """更新进度"""
        self.current += n
        self._display()
    
    def _display(self) -> None:
        """显示进度条"""
        if self.total == 0:
            return
            
        percent = (self.current / self.total) * 100
        elapsed_time = time.time() - self.start_time
        
        if self.current > 0:
            eta = (elapsed_time / self.current) * (self.total - self.current)
            eta_str = f"{int(eta)}s"
        else:
            eta_str = "N/A"
        
        bar_length = 30
        filled_length = int(bar_length * self.current // self.total)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f'\r{self.desc}: |{bar}| {percent:.1f}% ({self.current}/{self.total}) ETA: {eta_str}', end='')
        
        if self.current >= self.total:
            print()  # 换行


class VideoQualitySelector:
    """视频质量选择器"""
    
    QUALITY_MAP = {
        'fhd': {'name': '超清', 'priority': 4},
        'hd': {'name': '高清', 'priority': 3},
        'sd': {'name': '标清', 'priority': 2},
        'ld': {'name': '流畅', 'priority': 1}
    }
    
    @classmethod
    def select_best_quality(cls, available_qualities: List[str]) -> str:
        """选择最佳质量"""
        if not available_qualities:
            return 'sd'  # 默认标清
        
        # 按优先级排序
        sorted_qualities = sorted(
            available_qualities,
            key=lambda x: cls.QUALITY_MAP.get(x, {}).get('priority', 0),
            reverse=True
        )
        
        return sorted_qualities[0]
    
    @classmethod
    def get_quality_name(cls, quality_code: str) -> str:
        """获取质量名称"""
        return cls.QUALITY_MAP.get(quality_code, {}).get('name', '未知')


class M3U8Parser:
    """M3U8文件解析器"""
    
    @staticmethod
    def parse_m3u8(content: str, base_url: str = "") -> List[str]:
        """解析M3U8文件，返回TS文件URL列表"""
        ts_urls = []
        
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                if line.startswith('http'):
                    ts_urls.append(line)
                elif base_url:
                    ts_urls.append(base_url.rstrip('/') + '/' + line)
                else:
                    ts_urls.append(line)
        
        return ts_urls
    
    @staticmethod
    def get_base_url(m3u8_url: str) -> str:
        """获取M3U8的基础URL"""
        return m3u8_url.rsplit('/', 1)[0] + '/'


class Logger:
    """日志管理器"""
    
    @staticmethod
    def setup_logger(name: str, log_file: str = None, level: str = 'INFO') -> logging.Logger:
        """设置日志器"""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, level.upper()))
        
        # 文件处理器
        if log_file:
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper()))
            
            # 格式化器
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            logger.addHandler(file_handler)
        
        # 控制台格式化器
        console_formatter = logging.Formatter(
            '%(levelname)s - %(message)s'
        )
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
        
        return logger


class ConfigValidator:
    """配置验证器"""
    
    @staticmethod
    def validate_url(url: str) -> bool:
        """验证URL格式"""
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False
    
    @staticmethod
    def validate_output_path(path: str) -> bool:
        """验证输出路径"""
        try:
            directory = os.path.dirname(path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
            return True
        except Exception:
            return False


def format_size(size_bytes: int) -> str:
    """格式化文件大小"""
    if size_bytes == 0:
        return "0B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f}{size_names[i]}"


def format_duration(seconds: int) -> str:
    """格式化时间长度"""
    hours = seconds // 3600
    minutes = (seconds % 3600) // 60
    seconds = seconds % 60
    
    if hours > 0:
        return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
    else:
        return f"{minutes:02d}:{seconds:02d}"
