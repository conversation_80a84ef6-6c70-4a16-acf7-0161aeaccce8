@echo off
chcp 65001 >nul

echo 🚀 启动企业级视频平台...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装
    pause
    exit /b 1
)

REM 检查虚拟环境
if "%VIRTUAL_ENV%"=="" (
    echo ⚠️  建议在虚拟环境中运行
    set /p continue="是否继续？(y/N): "
    if /i not "%continue%"=="y" (
        exit /b 1
    )
)

REM 安装依赖
echo 📦 安装依赖...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

REM 初始化数据库
echo 🗄️  初始化数据库...
python scripts/init_db.py
if errorlevel 1 (
    echo ❌ 数据库初始化失败
    pause
    exit /b 1
)

REM 启动应用
echo 🌟 启动应用...
echo 访问地址: http://localhost:8000
echo API文档: http://localhost:8000/docs
echo 按 Ctrl+C 停止服务

uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
