# 🎯 Day 2: FastAPI + HTML解析 零基础学习指南

## 📋 学习策略：从实例开始，理论跟上

### 🚀 第一步：理解什么是API (5分钟)

**简单理解**：
- **传统方式**：网页直接返回HTML → 用户看到完整页面
- **API方式**：只返回数据(JSON) → 前端/手机App自己渲染页面

**好处**：一套后端，支持网页、手机App、小程序等多种前端

**举例**：
```python
# 传统网页：返回完整HTML
return "<html><body>用户名：张三</body></html>"

# API方式：只返回数据
return {"username": "张三", "email": "<EMAIL>"}
```

---

## 🌅 上午主线 (9:00-12:00)：FastAPI快速上手

### 🎯 第二步：5分钟创建第一个API

#### 最简单的例子
```python
# 文件：app/main.py
from fastapi import FastAPI

app = FastAPI()

@app.get("/")  # GET请求，访问根路径
def hello():
    return {"message": "Hello World"}

@app.get("/user/{user_id}")  # 路径参数
def get_user(user_id: int):
    return {"user_id": user_id, "name": f"用户{user_id}"}
```

**运行命令**：
```bash
pip install fastapi uvicorn
uvicorn main:app --reload
```

**测试**：浏览器访问 `http://localhost:8000`

---

### 🛠️ 第三步：动手练习 - 用户注册登录API (45分钟)

#### 练习1：数据验证模型 (15分钟)
```python
# 文件：app/models.py
from pydantic import BaseModel, EmailStr
from typing import Optional

class UserRegister(BaseModel):
    username: str
    email: EmailStr  # 自动验证邮箱格式
    password: str
    
class UserLogin(BaseModel):
    username: str
    password: str

class UserResponse(BaseModel):
    id: int
    username: str
    email: str
    # 注意：不返回密码
```

**理解要点**：
- `BaseModel` = 数据验证基类
- `EmailStr` = 自动验证邮箱格式
- 请求模型 vs 响应模型要分开

#### 练习2：API接口实现 (30分钟)
```python
# 文件：app/main.py
from fastapi import FastAPI, HTTPException
from models import UserRegister, UserLogin, UserResponse

app = FastAPI()

# 模拟数据库
fake_users_db = []
user_id_counter = 1

@app.post("/register", response_model=UserResponse)
def register_user(user: UserRegister):
    global user_id_counter
    
    # 检查用户名是否已存在
    for existing_user in fake_users_db:
        if existing_user["username"] == user.username:
            raise HTTPException(status_code=400, detail="用户名已存在")
    
    # 创建新用户
    new_user = {
        "id": user_id_counter,
        "username": user.username,
        "email": user.email,
        "password": user.password  # 实际项目中要加密
    }
    fake_users_db.append(new_user)
    user_id_counter += 1
    
    # 返回用户信息（不包含密码）
    return UserResponse(**new_user)

@app.post("/login")
def login_user(user: UserLogin):
    # 查找用户
    for db_user in fake_users_db:
        if (db_user["username"] == user.username and 
            db_user["password"] == user.password):
            return {"message": "登录成功", "user_id": db_user["id"]}
    
    raise HTTPException(status_code=401, detail="用户名或密码错误")
```

**测试方法**：
1. 启动服务：`uvicorn main:app --reload`
2. 访问：`http://localhost:8000/docs` (自动生成的API文档)
3. 在文档页面测试注册和登录

---

## 🌞 下午辅线 (14:00-17:00)：HTML解析技术

### 🎯 第四步：理解HTML解析 (5分钟)

**简单理解**：
- **HTML** = 网页的结构代码
- **解析** = 从HTML中提取我们需要的数据
- **工具** = BeautifulSoup (最简单易用)

**举例**：
```html
<!-- 原始HTML -->
<div class="movie">
    <h3>肖申克的救赎</h3>
    <span class="rating">9.7</span>
</div>

<!-- 我们想要的数据 -->
电影名：肖申克的救赎
评分：9.7
```

---

### 🛠️ 第五步：BeautifulSoup实战练习 (90分钟)

#### 练习1：基础解析 (30分钟)
```python
# 文件：day2_bs4_basic.py
from bs4 import BeautifulSoup
import requests

# 示例HTML
html_content = """
<html>
<body>
    <div class="movie" id="movie1">
        <h3 class="title">肖申克的救赎</h3>
        <span class="rating">9.7</span>
        <p class="director">导演: 弗兰克·德拉邦特</p>
    </div>
    <div class="movie" id="movie2">
        <h3 class="title">霸王别姬</h3>
        <span class="rating">9.6</span>
        <p class="director">导演: 陈凯歌</p>
    </div>
</body>
</html>
"""

# 解析HTML
soup = BeautifulSoup(html_content, 'html.parser')

# 方法1：通过class查找
movies = soup.find_all('div', class_='movie')
for movie in movies:
    title = movie.find('h3', class_='title').text
    rating = movie.find('span', class_='rating').text
    director = movie.find('p', class_='director').text
    print(f"电影：{title}, 评分：{rating}, {director}")

# 方法2：CSS选择器
titles = soup.select('.movie .title')
for title in titles:
    print(f"标题：{title.text}")
```

#### 练习2：正则表达式提取 (30分钟)
```python
# 文件：day2_regex_practice.py
import re

# 示例文本
text = """
联系我们：
邮箱：<EMAIL>, <EMAIL>
电话：138-0013-8000, ************
网站：https://www.example.com, http://test.org
"""

# 提取邮箱
email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
emails = re.findall(email_pattern, text)
print("邮箱地址：", emails)

# 提取电话号码
phone_pattern = r'\b(?:\d{3}-\d{4}-\d{4}|\d{3}-\d{3}-\d{4})\b'
phones = re.findall(phone_pattern, text)
print("电话号码：", phones)

# 提取URL
url_pattern = r'https?://[^\s]+'
urls = re.findall(url_pattern, text)
print("网址：", urls)
```

#### 练习3：综合数据提取 (30分钟)
```python
# 文件：day2_data_extraction.py
import requests
from bs4 import BeautifulSoup
import json
import time

def extract_douban_movies():
    """提取豆瓣电影Top250第一页数据"""
    
    # 请求头，模拟浏览器
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # 获取页面（注意：实际使用时要遵守网站robots.txt）
    url = "https://movie.douban.com/top250"
    
    # 这里用模拟数据代替真实请求
    mock_html = """
    <div class="item">
        <div class="info">
            <div class="hd">
                <span class="title">肖申克的救赎</span>
            </div>
            <div class="star">
                <span class="rating_num">9.7</span>
            </div>
        </div>
    </div>
    """
    
    soup = BeautifulSoup(mock_html, 'html.parser')
    movies = []
    
    # 提取电影信息
    items = soup.find_all('div', class_='item')
    for item in items:
        try:
            title = item.find('span', class_='title').text.strip()
            rating = item.find('span', class_='rating_num').text.strip()
            
            movie = {
                'title': title,
                'rating': float(rating),
                'source': 'douban'
            }
            movies.append(movie)
        except AttributeError:
            # 处理缺失数据
            continue
    
    return movies

# 执行提取
movies = extract_douban_movies()

# 保存为JSON
with open('movies.json', 'w', encoding='utf-8') as f:
    json.dump(movies, f, ensure_ascii=False, indent=2)

print(f"提取了 {len(movies)} 部电影信息")
```

---

## ✅ 上午学习检查清单

### FastAPI基础概念 (必须理解)
- [ ] 知道什么是API和REST API
- [ ] 理解路径参数和查询参数
- [ ] 知道HTTP状态码的含义
- [ ] 理解请求模型和响应模型的区别

### 实践技能 (必须会做)
- [ ] 能创建基本的GET/POST接口
- [ ] 能使用Pydantic进行数据验证
- [ ] 能处理HTTP异常
- [ ] 能使用FastAPI自动文档

---

## ✅ 下午学习检查清单

### HTML解析概念 (必须理解)
- [ ] 知道HTML的基本结构
- [ ] 理解CSS选择器的作用
- [ ] 知道正则表达式的基本用法
- [ ] 理解数据清洗的重要性

### 实践技能 (必须会做)
- [ ] 能使用BeautifulSoup解析HTML
- [ ] 能使用不同的选择器查找元素
- [ ] 能处理缺失数据和异常
- [ ] 能将提取的数据保存为JSON

---

## 🚀 下一步行动

### 立即行动 (30分钟)
1. **FastAPI实践**：
   - 复制上面的用户注册API例子
   - 在本地运行并测试
   - 尝试修改返回的数据格式

2. **HTML解析实践**：
   - 运行BeautifulSoup基础例子
   - 尝试修改选择器
   - 观察不同方法的效果

### 深入练习 (60分钟)
1. **API扩展**：
   - 添加用户信息修改接口
   - 添加用户列表查询接口
   - 实现简单的权限验证

2. **爬虫扩展**：
   - 尝试解析其他网站结构
   - 添加更多数据字段提取
   - 实现数据去重和验证

**记住**：先跑通例子，再理解原理，遇到问题再查文档！
