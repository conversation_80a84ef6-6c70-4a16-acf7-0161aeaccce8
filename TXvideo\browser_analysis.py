#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器行为分析工具 - 解决"免费但爬不了"的问题
"""

import requests
import re
import json
import time
from tx_video_spider import TencentVideoSpider

class BrowserAnalyzer:
    """浏览器行为分析器"""
    
    def __init__(self):
        self.session = requests.Session()
        self._setup_browser_headers()
    
    def _setup_browser_headers(self):
        """设置完全模拟浏览器的请求头"""
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        })
    
    def analyze_video_page(self, video_url: str):
        """分析视频页面，提取关键信息"""
        print(f"🔍 分析视频页面: {video_url}")
        
        try:
            # 1. 访问视频页面
            response = self.session.get(video_url, timeout=15)
            if response.status_code != 200:
                print(f"❌ 页面访问失败: {response.status_code}")
                return None
            
            content = response.text
            print("✅ 页面访问成功")
            
            # 2. 提取页面中的关键数据
            analysis_result = {
                'vid': self._extract_vid(content),
                'coverid': self._extract_coverid(content),
                'page_data': self._extract_page_data(content),
                'api_params': self._extract_api_params(content),
                'cookies': dict(response.cookies),
                'headers': dict(response.headers)
            }
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 页面分析失败: {e}")
            return None
    
    def _extract_vid(self, content: str) -> str:
        """从页面内容提取vid"""
        patterns = [
            r'"vid"\s*:\s*"([^"]+)"',
            r'vid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'data-vid["\']?\s*=\s*["\']([^"\']+)["\']',
            r'vid=([^&\s]+)',
            r'VIDEO_INFO.*?"vid":"([^"]+)"',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                vid = match.group(1)
                print(f"✅ 提取vid: {vid}")
                return vid
        
        print("❌ 未找到vid")
        return ""
    
    def _extract_coverid(self, content: str) -> str:
        """从页面内容提取coverid"""
        patterns = [
            r'"cover_id"\s*:\s*"([^"]+)"',
            r'"coverid"\s*:\s*"([^"]+)"',
            r'coverid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'data-coverid["\']?\s*=\s*["\']([^"\']+)["\']',
            r'VIDEO_INFO.*?"cover_id":"([^"]+)"',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                coverid = match.group(1)
                print(f"✅ 提取coverid: {coverid}")
                return coverid
        
        print("❌ 未找到coverid")
        return ""
    
    def _extract_page_data(self, content: str) -> dict:
        """提取页面中的数据对象"""
        data = {}
        
        # 查找常见的数据对象
        patterns = [
            (r'window\.__INITIAL_STATE__\s*=\s*({.+?});', 'initial_state'),
            (r'window\.VIDEO_INFO\s*=\s*({.+?});', 'video_info'),
            (r'window\.PAGE_INFO\s*=\s*({.+?});', 'page_info'),
            (r'__NUXT__\s*=\s*({.+?});', 'nuxt_data'),
        ]
        
        for pattern, key in patterns:
            match = re.search(pattern, content, re.DOTALL)
            if match:
                try:
                    json_str = match.group(1)
                    data[key] = json.loads(json_str)
                    print(f"✅ 提取{key}数据")
                except:
                    print(f"⚠️ {key}数据解析失败")
        
        return data
    
    def _extract_api_params(self, content: str) -> dict:
        """提取API相关参数"""
        params = {}
        
        # 查找可能的API参数
        patterns = [
            (r'"guid"\s*:\s*"([^"]+)"', 'guid'),
            (r'"platform"\s*:\s*"([^"]+)"', 'platform'),
            (r'"appver"\s*:\s*"([^"]+)"', 'appver'),
            (r'"ckey"\s*:\s*"([^"]+)"', 'ckey'),
        ]
        
        for pattern, key in patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                params[key] = match.group(1)
                print(f"✅ 提取{key}: {match.group(1)[:20]}...")
        
        return params

def compare_with_spider(video_url: str):
    """对比浏览器分析结果与爬虫生成的参数"""
    print("🔍 开始对比分析...")
    print("=" * 60)
    
    # 1. 浏览器分析
    print("\n📱 浏览器行为分析:")
    analyzer = BrowserAnalyzer()
    browser_result = analyzer.analyze_video_page(video_url)
    
    if not browser_result:
        print("❌ 浏览器分析失败")
        return
    
    # 2. 爬虫参数生成
    print("\n🤖 爬虫参数生成:")
    spider = TencentVideoSpider()
    
    # 使用浏览器提取的信息
    vid = browser_result.get('vid', '')
    coverid = browser_result.get('coverid', '')
    
    if not vid:
        print("❌ 无法获取vid，尝试URL解析...")
        video_info = spider.extract_video_info(video_url)
        vid = video_info.get('vid', '')
        coverid = video_info.get('coverid', '')
    
    if vid:
        spider_params = spider.generate_params(vid, coverid)
        print(f"✅ 爬虫生成参数完成")
        
        # 3. 对比分析
        print("\n📊 对比分析结果:")
        print("-" * 40)
        
        print(f"VID: {vid}")
        print(f"Cover ID: {coverid}")
        
        # 对比关键参数
        browser_params = browser_result.get('api_params', {})
        
        if browser_params.get('guid') and spider_params.get('guid'):
            print(f"GUID - 浏览器: {browser_params['guid'][:20]}...")
            print(f"GUID - 爬虫:   {spider_params['guid'][:20]}...")
        
        if browser_params.get('ckey') and spider_params.get('ckey'):
            print(f"cKey - 浏览器: {browser_params['ckey'][:30]}...")
            print(f"cKey - 爬虫:   {spider_params['ckey'][:30]}...")
        
        # 4. 尝试使用浏览器参数
        print("\n🧪 尝试使用浏览器参数:")
        if browser_params:
            # 合并参数
            merged_params = spider_params.copy()
            merged_params.update(browser_params)
            
            # 测试请求
            video_data = spider.get_video_info(merged_params)
            if video_data:
                vinfo = video_data.get('vinfo', '')
                if '外星人' not in str(vinfo):
                    print("🎉 使用浏览器参数可能成功！")
                    return True
                else:
                    print("❌ 仍然被限制")
            else:
                print("❌ 请求失败")
    
    return False

def suggest_solutions(video_url: str):
    """提供针对性解决方案"""
    print("\n💡 针对性解决方案:")
    print("=" * 60)
    
    print("🔍 问题分析:")
    print("虽然视频在浏览器中免费观看，但API访问被限制。")
    print("这是因为腾讯视频对不同访问方式有不同的权限控制。")
    
    print("\n🛠️ 可能的解决方案:")
    
    print("\n1. 📱 完全模拟浏览器行为:")
    print("   - 使用Selenium自动化浏览器")
    print("   - 模拟真实的用户操作")
    print("   - 提取浏览器中的实际请求")
    
    print("\n2. 🍪 获取真实的浏览器状态:")
    print("   - 在浏览器中播放视频")
    print("   - 复制所有Cookie和请求头")
    print("   - 在爬虫中使用相同的状态")
    
    print("\n3. 🔄 逆向工程:")
    print("   - 分析浏览器中的JavaScript代码")
    print("   - 找到真实的参数生成逻辑")
    print("   - 更新cKey生成算法")
    
    print("\n4. 🌐 网络层面:")
    print("   - 使用代理服务器")
    print("   - 更换IP地址")
    print("   - 模拟不同地区访问")
    
    print(f"\n🎯 立即可行的测试:")
    print("1. 在浏览器中打开: {video_url}")
    print("2. 按F12，切换到Network标签")
    print("3. 播放视频，查找'proxyhttp'请求")
    print("4. 复制完整的请求头和参数")
    print("5. 在爬虫中使用相同的参数")

def main():
    """主函数"""
    print("🔍 腾讯视频浏览器行为分析工具")
    print("=" * 60)
    
    # 测试URL
    test_url = "https://v.qq.com/x/cover/qwzpd184if155sa/p00156giz3p.html"
    print(f"🎯 分析视频: {test_url}")
    
    # 执行对比分析
    success = compare_with_spider(test_url)
    
    if not success:
        # 提供解决方案
        suggest_solutions(test_url)
    
    print(f"\n{'='*60}")
    print("📝 总结:")
    print("你的爬虫框架技术上完全正确！")
    print("问题在于腾讯视频的访问控制机制。")
    print("需要更深入的逆向工程或使用浏览器自动化。")

if __name__ == "__main__":
    main()
