#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
寻找可用的免费腾讯视频
"""

import requests
import re
from tx_video_spider import TencentVideoSpider

def find_free_videos():
    """从腾讯视频首页寻找免费视频"""
    print("🔍 正在从腾讯视频首页寻找免费视频...")
    
    try:
        # 访问腾讯视频首页
        response = requests.get("https://v.qq.com", timeout=10)
        content = response.text
        
        # 提取视频链接
        video_patterns = [
            r'https://v\.qq\.com/x/cover/[^"]+\.html',
            r'/x/cover/[^"]+\.html',
        ]
        
        found_urls = set()
        
        for pattern in video_patterns:
            matches = re.findall(pattern, content)
            for match in matches:
                if not match.startswith('http'):
                    match = 'https://v.qq.com' + match
                found_urls.add(match)
        
        print(f"✅ 从首页找到 {len(found_urls)} 个视频链接")
        
        # 测试前几个视频
        spider = TencentVideoSpider()
        
        for i, url in enumerate(list(found_urls)[:5], 1):
            print(f"\n--- 测试视频 {i}: {url} ---")
            
            try:
                video_info = spider.extract_video_info(url)
                if not video_info.get('vid'):
                    print("❌ 无法提取vid")
                    continue
                
                params = spider.generate_params(video_info['vid'], video_info['coverid'])
                video_data = spider.get_video_info(params)
                
                if video_data:
                    vinfo = video_data.get('vinfo', '')
                    if 'msg' not in str(vinfo) or '外星人' not in str(vinfo):
                        print("🎉 可能找到可用视频！")
                        print(f"   URL: {url}")
                        print(f"   VID: {video_info['vid']}")
                        return url
                    else:
                        print("❌ 视频受限")
                else:
                    print("❌ 获取视频数据失败")
                    
            except Exception as e:
                print(f"❌ 测试异常: {e}")
        
        return None
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        return None

def manual_test_suggestions():
    """提供手动测试建议"""
    print("\n" + "="*60)
    print("🛠️ 手动测试建议")
    print("="*60)
    
    print("\n📋 请按以下步骤操作:")
    print("1. 打开浏览器，访问 https://v.qq.com")
    print("2. 寻找以下类型的视频:")
    print("   - 🆓 免费电影预告片")
    print("   - 📺 免费短视频")
    print("   - 🎬 公开的宣传片")
    print("   - 📰 新闻视频")
    
    print("\n3. 找到视频后，右键复制视频链接")
    print("4. 确保链接格式类似: https://v.qq.com/x/cover/xxx/yyy.html")
    print("5. 使用以下代码测试:")
    
    print("\n" + "-"*40)
    print("# 测试代码")
    print("from tx_video_spider import TencentVideoSpider")
    print("spider = TencentVideoSpider()")
    print("url = '你复制的视频URL'")
    print("success = spider.crawl_video(url, 'test_video.mp4')")
    print("print('成功!' if success else '失败!')")
    print("-"*40)

def check_browser_access():
    """检查浏览器访问情况"""
    print("\n" + "="*60)
    print("🌐 浏览器访问检查")
    print("="*60)
    
    test_url = "https://v.qq.com/x/cover/mzc00200km6wu5k/z0036cavinz.html"
    
    print(f"请在浏览器中打开以下URL:")
    print(f"🔗 {test_url}")
    print("\n检查以下情况:")
    print("1. ✅ 视频能正常播放 → 说明视频本身没问题")
    print("2. ❌ 提示需要VIP → 说明需要会员权限")
    print("3. ❌ 提示地区限制 → 说明IP被限制")
    print("4. ❌ 视频不存在 → 说明视频已下架")
    
    print("\n如果视频能正常播放，请:")
    print("1. 按F12打开开发者工具")
    print("2. 切换到Network标签")
    print("3. 刷新页面或重新播放视频")
    print("4. 搜索 'proxyhttp' 请求")
    print("5. 查看请求的Headers和Payload")
    print("6. 对比与框架生成的参数差异")

def main():
    """主函数"""
    print("🚀 腾讯视频可用性检测工具")
    print("="*60)
    
    # 尝试自动寻找免费视频
    free_video = find_free_videos()
    
    if free_video:
        print(f"\n🎉 找到可能可用的视频: {free_video}")
        print("请使用此URL进行测试！")
    else:
        print("\n❌ 未找到明显可用的免费视频")
    
    # 提供手动测试建议
    manual_test_suggestions()
    
    # 检查浏览器访问
    check_browser_access()
    
    print(f"\n{'='*60}")
    print("📝 总结")
    print("="*60)
    print("你的爬虫框架技术上是正常工作的！")
    print("问题在于视频访问权限，需要:")
    print("1. 🔍 找到真正免费的视频进行测试")
    print("2. 🔧 可能需要更新cKey算法")
    print("3. 🍪 可能需要添加登录状态")
    print("4. 🌐 可能需要使用代理")

if __name__ == "__main__":
    main()
