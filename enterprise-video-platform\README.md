# 🎬 企业级视频平台项目

## 📋 项目概述
基于TXvideo爬虫的企业级视频管理平台，集成数据库存储、RESTful API、用户管理、Docker部署等现代企业级技术栈。

## 🎯 技术栈
- **后端框架**: FastAPI + SQLAlchemy
- **数据库**: PostgreSQL / SQLite
- **认证**: JWT Token
- **容器化**: Docker + docker-compose
- **测试**: pytest
- **文档**: Swagger/OpenAPI

## 📁 项目结构
```
enterprise-video-platform/
├── app/                    # 应用核心代码
│   ├── __init__.py
│   ├── main.py            # FastAPI应用入口
│   ├── config.py          # 配置管理
│   ├── database.py        # 数据库连接
│   ├── models/            # 数据模型
│   ├── schemas/           # Pydantic模型
│   ├── api/               # API路由
│   ├── services/          # 业务逻辑
│   ├── utils/             # 工具函数
│   └── spider/            # 爬虫模块
├── tests/                 # 测试代码
├── docker/                # Docker配置
├── docs/                  # 项目文档
├── scripts/               # 脚本文件
├── requirements.txt       # Python依赖
├── docker-compose.yml     # 容器编排
├── Dockerfile            # 容器镜像
└── .env.example          # 环境变量示例
```

## 🚀 快速开始

### 1. 环境准备
```bash
# 克隆项目
git clone <repository-url>
cd enterprise-video-platform

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境
```bash
# 复制环境变量文件
cp .env.example .env

# 编辑配置
vim .env
```

### 3. 数据库初始化
```bash
# 创建数据库表
python scripts/init_db.py

# 运行数据库迁移
alembic upgrade head
```

### 4. 启动应用
```bash
# 开发模式
uvicorn app.main:app --reload

# 生产模式
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

### 5. Docker部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📖 API文档
启动应用后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 🧪 测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_api.py

# 生成覆盖率报告
pytest --cov=app tests/
```

## 📚 学习路径
1. **第一周**: 数据库集成与API开发
2. **第二周**: Docker部署与DevOps

详细学习计划请参考：[实习技能冲刺计划.md](../实习技能冲刺计划.md)

## 🤝 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证
MIT License
