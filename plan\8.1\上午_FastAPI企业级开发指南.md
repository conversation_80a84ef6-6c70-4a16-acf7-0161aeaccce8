# 🚀 FastAPI企业级开发指南 (上午专用)

## 🎯 学习目标：从零到企业级API开发

### 📚 第一步：理解现代API开发 (10分钟)

**什么是FastAPI？**
- FastAPI = 现代、快速的Python Web框架
- 自动生成API文档 (Swagger UI)
- 内置数据验证和类型检查
- 异步支持，性能接近Node.js

**为什么选择FastAPI？**
```python
# 传统Flask写法
@app.route('/user/<int:user_id>', methods=['GET'])
def get_user(user_id):
    # 手动验证参数类型
    if not isinstance(user_id, int):
        return {"error": "Invalid user_id"}
    return {"user_id": user_id}

# FastAPI写法 - 自动验证
@app.get("/user/{user_id}")
def get_user(user_id: int):  # 自动类型验证
    return {"user_id": user_id}
```

**企业级特性**：
- 自动API文档生成
- 数据验证和序列化
- 依赖注入系统
- 中间件支持
- 测试友好

---

## 🛠️ 第二步：环境搭建与第一个API (15分钟)

### 安装依赖
```bash
# 创建虚拟环境
python -m venv fastapi_env
fastapi_env\Scripts\activate  # Windows
# source fastapi_env/bin/activate  # Linux/Mac

# 安装核心包
pip install fastapi uvicorn[standard]
pip install python-multipart  # 表单数据支持
pip install email-validator   # 邮箱验证
```

### 创建第一个API
```python
# 文件：app/main.py
from fastapi import FastAPI
from datetime import datetime

# 创建FastAPI实例
app = FastAPI(
    title="视频平台API",
    description="企业级视频平台后端API",
    version="1.0.0"
)

@app.get("/")
def root():
    """根路径 - API健康检查"""
    return {
        "message": "视频平台API运行正常",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.get("/health")
def health_check():
    """健康检查接口"""
    return {"status": "healthy", "service": "video-platform-api"}
```

**启动服务**：
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**测试访问**：
- API接口：http://localhost:8000
- 自动文档：http://localhost:8000/docs
- 备用文档：http://localhost:8000/redoc

---

## 🎯 第三步：数据模型与验证 (30分钟)

### Pydantic数据模型
```python
# 文件：app/models.py
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime
from enum import Enum

class UserRole(str, Enum):
    """用户角色枚举"""
    ADMIN = "admin"
    USER = "user"
    MODERATOR = "moderator"

class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="真实姓名")
    role: UserRole = Field(UserRole.USER, description="用户角色")

class UserCreate(UserBase):
    """用户创建模型"""
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    confirm_password: str = Field(..., description="确认密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values, **kwargs):
        if 'password' in values and v != values['password']:
            raise ValueError('密码不匹配')
        return v
    
    @validator('username')
    def username_alphanumeric(cls, v):
        if not v.replace('_', '').isalnum():
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v

class UserLogin(BaseModel):
    """用户登录模型"""
    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")

class UserResponse(UserBase):
    """用户响应模型"""
    id: int
    is_active: bool = True
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True  # 允许从ORM对象创建

class VideoBase(BaseModel):
    """视频基础模型"""
    title: str = Field(..., min_length=1, max_length=255, description="视频标题")
    description: Optional[str] = Field(None, max_length=1000, description="视频描述")
    url: str = Field(..., description="视频URL")
    duration: Optional[int] = Field(None, ge=0, description="视频时长(秒)")
    tags: List[str] = Field(default=[], description="标签列表")

class VideoCreate(VideoBase):
    """视频创建模型"""
    pass

class VideoResponse(VideoBase):
    """视频响应模型"""
    id: int
    user_id: int
    created_at: datetime
    view_count: int = 0
    like_count: int = 0
    
    class Config:
        from_attributes = True
```

### 响应模型
```python
# 文件：app/schemas.py
from pydantic import BaseModel
from typing import Optional, Any, List

class APIResponse(BaseModel):
    """标准API响应格式"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[Any] = None
    error_code: Optional[str] = None

class PaginatedResponse(BaseModel):
    """分页响应格式"""
    items: List[Any]
    total: int
    page: int
    size: int
    pages: int

class ErrorResponse(BaseModel):
    """错误响应格式"""
    success: bool = False
    message: str
    error_code: str
    details: Optional[dict] = None
```

---

## 🔧 第四步：路由与接口开发 (45分钟)

### 用户管理接口
```python
# 文件：app/routers/users.py
from fastapi import APIRouter, HTTPException, Depends, status
from typing import List
from app.models import UserCreate, UserLogin, UserResponse
from app.schemas import APIResponse, PaginatedResponse
from app.database import get_db  # 假设有数据库连接

router = APIRouter(
    prefix="/users",
    tags=["用户管理"],
    responses={404: {"description": "用户不存在"}}
)

# 模拟数据库
fake_users_db = []
user_id_counter = 1

@router.post("/register", response_model=APIResponse, status_code=status.HTTP_201_CREATED)
async def register_user(user: UserCreate):
    """用户注册"""
    global user_id_counter
    
    # 检查用户名是否已存在
    for existing_user in fake_users_db:
        if existing_user["username"] == user.username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        if existing_user["email"] == user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
    
    # 创建新用户
    new_user = {
        "id": user_id_counter,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "role": user.role,
        "password": user.password,  # 实际项目中要加密
        "is_active": True,
        "created_at": datetime.now(),
        "last_login": None
    }
    fake_users_db.append(new_user)
    user_id_counter += 1
    
    # 返回用户信息（不包含密码）
    user_response = UserResponse(**new_user)
    return APIResponse(
        message="用户注册成功",
        data=user_response
    )

@router.post("/login", response_model=APIResponse)
async def login_user(user: UserLogin):
    """用户登录"""
    # 查找用户（支持用户名或邮箱登录）
    db_user = None
    for u in fake_users_db:
        if (u["username"] == user.username or u["email"] == user.username) and u["password"] == user.password:
            db_user = u
            break
    
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误"
        )
    
    if not db_user["is_active"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="账户已被禁用"
        )
    
    # 更新最后登录时间
    db_user["last_login"] = datetime.now()
    
    return APIResponse(
        message="登录成功",
        data={
            "user_id": db_user["id"],
            "username": db_user["username"],
            "role": db_user["role"],
            "access_token": f"fake_token_{db_user['id']}"  # 实际项目中使用JWT
        }
    )

@router.get("/", response_model=PaginatedResponse)
async def get_users(page: int = 1, size: int = 10):
    """获取用户列表"""
    start = (page - 1) * size
    end = start + size
    
    users = [UserResponse(**user) for user in fake_users_db[start:end]]
    total = len(fake_users_db)
    pages = (total + size - 1) // size
    
    return PaginatedResponse(
        items=users,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

@router.get("/{user_id}", response_model=APIResponse)
async def get_user(user_id: int):
    """获取用户详情"""
    for user in fake_users_db:
        if user["id"] == user_id:
            return APIResponse(
                data=UserResponse(**user)
            )
    
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="用户不存在"
    )

@router.put("/{user_id}", response_model=APIResponse)
async def update_user(user_id: int, user_update: UserBase):
    """更新用户信息"""
    for i, user in enumerate(fake_users_db):
        if user["id"] == user_id:
            # 更新用户信息
            fake_users_db[i].update(user_update.dict(exclude_unset=True))
            return APIResponse(
                message="用户信息更新成功",
                data=UserResponse(**fake_users_db[i])
            )
    
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="用户不存在"
    )

@router.delete("/{user_id}", response_model=APIResponse)
async def delete_user(user_id: int):
    """删除用户"""
    for i, user in enumerate(fake_users_db):
        if user["id"] == user_id:
            deleted_user = fake_users_db.pop(i)
            return APIResponse(
                message="用户删除成功",
                data={"deleted_user_id": deleted_user["id"]}
            )
    
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="用户不存在"
    )
```

### 主应用集成
```python
# 文件：app/main.py (更新版)
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from app.routers import users
from datetime import datetime

# 创建FastAPI实例
app = FastAPI(
    title="视频平台API",
    description="企业级视频平台后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该指定具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "message": exc.detail,
            "error_code": f"HTTP_{exc.status_code}"
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request, exc):
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": "服务器内部错误",
            "error_code": "INTERNAL_SERVER_ERROR"
        }
    )

# 包含路由
app.include_router(users.router, prefix="/api/v1")

@app.get("/")
def root():
    """根路径 - API健康检查"""
    return {
        "message": "视频平台API运行正常",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "docs_url": "/docs"
    }

@app.get("/api/v1/health")
def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": "video-platform-api",
        "timestamp": datetime.now().isoformat()
    }
```

---

## 🎯 第五步：中间件与依赖注入 (30分钟)

### 认证中间件
```python
# 文件：app/dependencies.py
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials
from typing import Optional

security = HTTPBearer()

def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """获取当前用户（简化版JWT验证）"""
    token = credentials.credentials

    # 简化的token验证（实际项目中使用JWT）
    if not token.startswith("fake_token_"):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌"
        )

    try:
        user_id = int(token.replace("fake_token_", ""))
        # 这里应该从数据库查询用户信息
        return {"user_id": user_id, "username": f"user_{user_id}"}
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的访问令牌格式"
        )

def get_admin_user(current_user: dict = Depends(get_current_user)):
    """获取管理员用户"""
    # 这里应该检查用户角色
    if current_user.get("role") != "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user
```

### 请求日志中间件
```python
# 文件：app/middleware.py
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()

        # 记录请求信息
        logger.info(f"请求开始: {request.method} {request.url}")

        # 处理请求
        response = await call_next(request)

        # 计算处理时间
        process_time = time.time() - start_time

        # 记录响应信息
        logger.info(
            f"请求完成: {request.method} {request.url} "
            f"状态码: {response.status_code} "
            f"处理时间: {process_time:.4f}s"
        )

        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)

        return response

# 在main.py中添加中间件
# app.add_middleware(LoggingMiddleware)
```

---

## 🧪 第六步：测试与验证 (20分钟)

### API测试脚本
```python
# 文件：test_api.py
import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_user_registration():
    """测试用户注册"""
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "password": "password123",
        "confirm_password": "password123",
        "full_name": "测试用户"
    }

    response = requests.post(f"{BASE_URL}/users/register", json=user_data)
    print(f"注册响应: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    return response.json()

def test_user_login():
    """测试用户登录"""
    login_data = {
        "username": "testuser",
        "password": "password123"
    }

    response = requests.post(f"{BASE_URL}/users/login", json=login_data)
    print(f"登录响应: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))
    return response.json()

def test_get_users():
    """测试获取用户列表"""
    response = requests.get(f"{BASE_URL}/users/")
    print(f"用户列表响应: {response.status_code}")
    print(json.dumps(response.json(), indent=2, ensure_ascii=False))

if __name__ == "__main__":
    print("=== 测试用户注册 ===")
    test_user_registration()

    print("\n=== 测试用户登录 ===")
    test_user_login()

    print("\n=== 测试获取用户列表 ===")
    test_get_users()
```

### 使用Swagger UI测试
1. 启动服务：`uvicorn app.main:app --reload`
2. 访问：http://localhost:8000/docs
3. 测试步骤：
   - 点击"用户管理"展开接口
   - 测试POST /users/register 注册用户
   - 测试POST /users/login 用户登录
   - 测试GET /users/ 获取用户列表
   - 测试GET /users/{user_id} 获取用户详情

---

## ✅ 上午学习检查清单

### 核心概念理解 (必须掌握)
- [ ] 理解FastAPI的核心优势
- [ ] 掌握Pydantic数据验证
- [ ] 理解路径参数和查询参数
- [ ] 知道HTTP状态码的正确使用
- [ ] 理解请求/响应模型的设计

### 实践技能 (必须会做)
- [ ] 能创建FastAPI应用实例
- [ ] 能定义数据模型和验证规则
- [ ] 能实现CRUD接口
- [ ] 能处理HTTP异常
- [ ] 能使用依赖注入
- [ ] 能添加中间件

### 企业级特性 (重要掌握)
- [ ] 能设计标准化API响应格式
- [ ] 能实现分页查询
- [ ] 能添加CORS支持
- [ ] 能实现全局异常处理
- [ ] 能使用自动API文档

---

## 🚀 上午总结与下午预告

### 🎯 上午成果
完成了企业级FastAPI开发的核心技能：
1. ✅ 创建了标准化的API项目结构
2. ✅ 实现了完整的用户管理CRUD接口
3. ✅ 掌握了数据验证和错误处理
4. ✅ 学会了中间件和依赖注入

### 🌞 下午预告
下午将学习HTML解析技术：
- BeautifulSoup网页解析
- 正则表达式数据提取
- 爬虫数据存储
- 与FastAPI集成实战

### 💡 实践建议
1. **立即实践** (15分钟)：
   - 启动FastAPI服务
   - 使用Swagger UI测试所有接口
   - 尝试修改数据模型

2. **扩展练习** (15分钟)：
   - 添加视频管理接口
   - 实现用户权限验证
   - 添加更多数据验证规则

**记住**：FastAPI的强大在于其自动化特性，多使用类型注解和Pydantic模型！
