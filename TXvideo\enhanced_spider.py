#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版腾讯视频爬虫 - 添加反反爬特征
"""

from tx_video_spider import TencentVideoSpider
import time
import random
import json
from typing import Dict, Optional

class EnhancedTencentSpider(TencentVideoSpider):
    """增强版腾讯视频爬虫"""
    
    def __init__(self):
        super().__init__()
        self._setup_enhanced_headers()
        self._setup_session_config()
    
    def _setup_enhanced_headers(self):
        """设置更真实的请求头"""
        # 随机选择User-Agent
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        
        selected_ua = random.choice(user_agents)
        
        # 更完整的请求头
        enhanced_headers = {
            'User-Agent': selected_ua,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,zh-TW;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        }
        
        self.session.headers.update(enhanced_headers)
    
    def _setup_session_config(self):
        """配置会话参数"""
        # 设置超时
        self.session.timeout = 30
        
        # 添加一些常见的Cookie
        common_cookies = {
            'pgv_pvid': f'{random.randint(1000000000, 9999999999)}',
            'ts_refer': 'www.baidu.com',
            'ts_last': str(int(time.time())),
        }
        
        self.session.cookies.update(common_cookies)
    
    def _random_delay(self, min_delay=1, max_delay=3):
        """随机延迟，模拟人类行为"""
        delay = random.uniform(min_delay, max_delay)
        print(f"⏰ 模拟人类行为，等待 {delay:.1f} 秒...")
        time.sleep(delay)
    
    def enhanced_extract_video_info(self, video_url: str) -> Dict[str, str]:
        """增强版视频信息提取"""
        print("🔍 增强版信息提取...")
        
        # 先访问主页，建立会话
        try:
            print("📱 建立会话连接...")
            self.session.get('https://v.qq.com', timeout=10)
            self._random_delay(0.5, 1.5)
        except:
            pass
        
        # 然后提取视频信息
        return self.extract_video_info(video_url)
    
    def enhanced_generate_params(self, vid: str, coverid: str) -> Dict[str, str]:
        """增强版参数生成"""
        print("🔧 增强版参数生成...")
        
        # 使用基础方法生成参数
        params = self.generate_params(vid, coverid)
        
        # 添加一些额外的参数
        current_time = int(time.time())
        params.update({
            'ts': str(current_time),
            'random': str(random.randint(100000, 999999)),
        })
        
        return params
    
    def enhanced_get_video_info(self, params: Dict[str, str]) -> Optional[Dict]:
        """增强版视频信息获取"""
        print("📊 增强版数据获取...")
        
        # 添加延迟
        self._random_delay(1, 2)
        
        # 先访问视频页面
        try:
            video_page_url = f"https://v.qq.com/x/cover/{params['coverid']}.html"
            print(f"📄 访问视频页面: {video_page_url}")
            self.session.get(video_page_url, timeout=10)
            self._random_delay(0.5, 1.5)
        except:
            pass
        
        # 然后获取视频信息
        return self.get_video_info(params)
    
    def crawl_video_enhanced(self, video_url: str, output_path: str = None) -> bool:
        """增强版视频爬取"""
        try:
            print("🚀 启动增强版爬虫...")
            print(f"🎯 目标视频: {video_url}")
            
            # 1. 增强版信息提取
            video_info = self.enhanced_extract_video_info(video_url)
            if not video_info:
                print("❌ 视频信息提取失败")
                return False
            
            print(f"✅ 提取成功: vid={video_info['vid']}, coverid={video_info['coverid']}")
            
            # 2. 增强版参数生成
            params = self.enhanced_generate_params(video_info['vid'], video_info['coverid'])
            if not params:
                print("❌ 参数生成失败")
                return False
            
            print("✅ 参数生成成功")
            
            # 3. 增强版数据获取
            video_data = self.enhanced_get_video_info(params)
            if not video_data:
                print("❌ 视频数据获取失败")
                return False
            
            print("✅ 数据获取成功")
            
            # 4. 解析视频URL
            video_urls = self.parse_video_urls(video_data)
            if not video_urls:
                print("❌ 未找到视频流URL")
                
                # 尝试不同的解析策略
                print("🔄 尝试备用解析策略...")
                video_urls = self._alternative_parse(video_data)
                
                if not video_urls:
                    return False
            
            print(f"✅ 找到 {len(video_urls)} 个视频流")
            
            # 5. 下载视频
            if not output_path:
                output_path = f"enhanced_video_{int(time.time())}.mp4"
            
            print(f"📥 开始下载到: {output_path}")
            return self.download_video(video_urls[0], output_path)
            
        except Exception as e:
            print(f"❌ 增强版爬取失败: {e}")
            return False
    
    def _alternative_parse(self, video_data: Dict) -> list:
        """备用解析策略"""
        video_urls = []
        
        try:
            # 策略1: 深度搜索所有可能的URL
            data_str = json.dumps(video_data)
            
            # 查找所有可能的视频URL模式
            import re
            patterns = [
                r'https?://[^"]*\.m3u8[^"]*',
                r'https?://[^"]*\.mp4[^"]*',
                r'https?://[^"]*\.flv[^"]*',
                r'"url":"([^"]*)"',
                r'"playurl":"([^"]*)"',
                r'"src":"([^"]*)"',
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, data_str)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0] if match else ""
                    
                    if match and ('video' in match.lower() or 
                                 '.m3u8' in match or 
                                 '.mp4' in match or 
                                 '.flv' in match):
                        clean_url = match.replace('\\/', '/')
                        if clean_url not in video_urls:
                            video_urls.append(clean_url)
                            print(f"🔍 备用策略找到: {clean_url[:80]}...")
            
        except Exception as e:
            print(f"⚠️ 备用解析异常: {e}")
        
        return video_urls
    
    def test_multiple_strategies(self, video_url: str) -> bool:
        """测试多种策略"""
        print("🧪 测试多种爬取策略...")
        
        strategies = [
            ("标准策略", self.crawl_video),
            ("增强策略", self.crawl_video_enhanced),
        ]
        
        for strategy_name, strategy_func in strategies:
            print(f"\n📋 尝试{strategy_name}...")
            try:
                success = strategy_func(video_url, f"{strategy_name}_output.mp4")
                if success:
                    print(f"🎉 {strategy_name}成功！")
                    return True
                else:
                    print(f"❌ {strategy_name}失败")
            except Exception as e:
                print(f"❌ {strategy_name}异常: {e}")
        
        return False

def main():
    """测试增强版爬虫"""
    print("🚀 增强版腾讯视频爬虫测试")
    print("=" * 50)
    
    # 创建增强版爬虫
    spider = EnhancedTencentSpider()
    
    # 测试URL
    test_url = input("请输入测试视频URL: ").strip()
    if not test_url:
        test_url = "https://v.qq.com/x/cover/qwzpd184if155sa.html"
    
    print(f"🎯 测试URL: {test_url}")
    
    # 测试多种策略
    success = spider.test_multiple_strategies(test_url)
    
    if success:
        print("\n🎉 测试成功！找到了可行的策略。")
    else:
        print("\n❌ 所有策略都失败了。")
        print("💡 建议:")
        print("1. 尝试其他免费视频URL")
        print("2. 检查网络连接")
        print("3. 使用代理服务器")

if __name__ == "__main__":
    main()
