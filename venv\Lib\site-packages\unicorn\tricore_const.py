# For Unicorn Engine. AUTO-GENERATED FILE, DO NOT EDIT [tricore_const.py]

# TRICORE CPU

UC_CPU_TRICORE_TC1796 = 0
UC_CPU_TRICORE_TC1797 = 1
UC_CPU_TRICORE_TC27X = 2
UC_CPU_TRICORE_ENDING = 3

# TRICORE registers

UC_TRICORE_REG_INVALID = 0
UC_TRICORE_REG_A0 = 1
UC_TRICORE_REG_A1 = 2
UC_TRICORE_REG_A2 = 3
UC_TRICORE_REG_A3 = 4
UC_TRICORE_REG_A4 = 5
UC_TRICORE_REG_A5 = 6
UC_TRICORE_REG_A6 = 7
UC_TRICORE_REG_A7 = 8
UC_TRICORE_REG_A8 = 9
UC_TRICORE_REG_A9 = 10
UC_TRICORE_REG_A10 = 11
UC_TRICORE_REG_A11 = 12
UC_TRICORE_REG_A12 = 13
UC_TRICORE_REG_A13 = 14
UC_TRICORE_REG_A14 = 15
UC_TRICORE_REG_A15 = 16
UC_TRICORE_REG_D0 = 17
UC_TRICORE_REG_D1 = 18
UC_TRICORE_REG_D2 = 19
UC_TRICORE_REG_D3 = 20
UC_TRICORE_REG_D4 = 21
UC_TRICORE_REG_D5 = 22
UC_TRICORE_REG_D6 = 23
UC_TRICORE_REG_D7 = 24
UC_TRICORE_REG_D8 = 25
UC_TRICORE_REG_D9 = 26
UC_TRICORE_REG_D10 = 27
UC_TRICORE_REG_D11 = 28
UC_TRICORE_REG_D12 = 29
UC_TRICORE_REG_D13 = 30
UC_TRICORE_REG_D14 = 31
UC_TRICORE_REG_D15 = 32
UC_TRICORE_REG_PCXI = 33
UC_TRICORE_REG_PSW = 34
UC_TRICORE_REG_PSW_USB_C = 35
UC_TRICORE_REG_PSW_USB_V = 36
UC_TRICORE_REG_PSW_USB_SV = 37
UC_TRICORE_REG_PSW_USB_AV = 38
UC_TRICORE_REG_PSW_USB_SAV = 39
UC_TRICORE_REG_PC = 40
UC_TRICORE_REG_SYSCON = 41
UC_TRICORE_REG_CPU_ID = 42
UC_TRICORE_REG_BIV = 43
UC_TRICORE_REG_BTV = 44
UC_TRICORE_REG_ISP = 45
UC_TRICORE_REG_ICR = 46
UC_TRICORE_REG_FCX = 47
UC_TRICORE_REG_LCX = 48
UC_TRICORE_REG_COMPAT = 49
UC_TRICORE_REG_DPR0_U = 50
UC_TRICORE_REG_DPR1_U = 51
UC_TRICORE_REG_DPR2_U = 52
UC_TRICORE_REG_DPR3_U = 53
UC_TRICORE_REG_DPR0_L = 54
UC_TRICORE_REG_DPR1_L = 55
UC_TRICORE_REG_DPR2_L = 56
UC_TRICORE_REG_DPR3_L = 57
UC_TRICORE_REG_CPR0_U = 58
UC_TRICORE_REG_CPR1_U = 59
UC_TRICORE_REG_CPR2_U = 60
UC_TRICORE_REG_CPR3_U = 61
UC_TRICORE_REG_CPR0_L = 62
UC_TRICORE_REG_CPR1_L = 63
UC_TRICORE_REG_CPR2_L = 64
UC_TRICORE_REG_CPR3_L = 65
UC_TRICORE_REG_DPM0 = 66
UC_TRICORE_REG_DPM1 = 67
UC_TRICORE_REG_DPM2 = 68
UC_TRICORE_REG_DPM3 = 69
UC_TRICORE_REG_CPM0 = 70
UC_TRICORE_REG_CPM1 = 71
UC_TRICORE_REG_CPM2 = 72
UC_TRICORE_REG_CPM3 = 73
UC_TRICORE_REG_MMU_CON = 74
UC_TRICORE_REG_MMU_ASI = 75
UC_TRICORE_REG_MMU_TVA = 76
UC_TRICORE_REG_MMU_TPA = 77
UC_TRICORE_REG_MMU_TPX = 78
UC_TRICORE_REG_MMU_TFA = 79
UC_TRICORE_REG_BMACON = 80
UC_TRICORE_REG_SMACON = 81
UC_TRICORE_REG_DIEAR = 82
UC_TRICORE_REG_DIETR = 83
UC_TRICORE_REG_CCDIER = 84
UC_TRICORE_REG_MIECON = 85
UC_TRICORE_REG_PIEAR = 86
UC_TRICORE_REG_PIETR = 87
UC_TRICORE_REG_CCPIER = 88
UC_TRICORE_REG_DBGSR = 89
UC_TRICORE_REG_EXEVT = 90
UC_TRICORE_REG_CREVT = 91
UC_TRICORE_REG_SWEVT = 92
UC_TRICORE_REG_TR0EVT = 93
UC_TRICORE_REG_TR1EVT = 94
UC_TRICORE_REG_DMS = 95
UC_TRICORE_REG_DCX = 96
UC_TRICORE_REG_DBGTCR = 97
UC_TRICORE_REG_CCTRL = 98
UC_TRICORE_REG_CCNT = 99
UC_TRICORE_REG_ICNT = 100
UC_TRICORE_REG_M1CNT = 101
UC_TRICORE_REG_M2CNT = 102
UC_TRICORE_REG_M3CNT = 103
UC_TRICORE_REG_ENDING = 104
UC_TRICORE_REG_GA0 = 1
UC_TRICORE_REG_GA1 = 2
UC_TRICORE_REG_GA8 = 9
UC_TRICORE_REG_GA9 = 10
UC_TRICORE_REG_SP = 11
UC_TRICORE_REG_LR = 12
UC_TRICORE_REG_IA = 16
UC_TRICORE_REG_ID = 32
