#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试免费视频链接
"""

from tx_video_spider import TencentVideoSpider

def test_free_video():
    """测试免费视频"""
    spider = TencentVideoSpider()
    video_url = 'https://v.qq.com/x/cover/qwzpd184if155sa.html'
    
    print('🚀 开始测试免费视频链接...')
    print(f'URL: {video_url}')
    print('='*60)
    
    try:
        # 1. 提取视频信息
        print("步骤1: 提取视频信息...")
        video_info = spider.extract_video_info(video_url)
        print(f'✅ 提取结果: vid={video_info.get("vid")}, coverid={video_info.get("coverid")}')
        
        if not video_info.get('vid'):
            print('❌ 无法提取vid，测试结束')
            return False
        
        # 2. 生成参数
        print("\n步骤2: 生成请求参数...")
        params = spider.generate_params(video_info['vid'], video_info['coverid'])
        print(f'✅ 参数生成完成，cKey长度: {len(params.get("ckey", ""))}')
        
        # 3. 获取视频数据
        print("\n步骤3: 获取视频数据...")
        video_data = spider.get_video_info(params)
        
        if not video_data:
            print('❌ 获取视频数据失败')
            return False
        
        print('✅ 获取视频数据成功')
        
        # 4. 解析视频URL
        print("\n步骤4: 解析视频URL...")
        video_urls = spider.parse_video_urls(video_data)
        print(f'📊 解析结果: {len(video_urls)} 个视频URL')
        
        if video_urls:
            print('🎉 成功找到视频流！')
            for i, url in enumerate(video_urls[:3], 1):
                print(f'  {i}. {url[:100]}...')
            
            # 5. 尝试下载
            print(f"\n步骤5: 尝试下载视频...")
            output_file = 'test_free_video.mp4'
            success = spider.download_video(video_urls[0], output_file)
            
            if success:
                print(f'🎉 视频下载成功！文件: {output_file}')
                return True
            else:
                print('❌ 视频下载失败')
                return False
        else:
            print('❌ 未找到视频流')
            return False
            
    except Exception as e:
        print(f'❌ 测试异常: {e}')
        return False

def main():
    """主函数"""
    success = test_free_video()
    
    print('\n' + '='*60)
    if success:
        print('🎉 测试成功！你的爬虫框架完全正常工作！')
        print('现在你可以用这个框架爬取其他腾讯视频了。')
    else:
        print('❌ 测试失败，可能需要进一步调试。')
        print('建议检查视频是否真的免费，或者尝试其他视频链接。')

if __name__ == "__main__":
    main()
