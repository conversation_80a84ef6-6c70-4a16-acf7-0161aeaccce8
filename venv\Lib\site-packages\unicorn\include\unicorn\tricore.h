/* This file is released under LGPL2.
   See COPYING.LGPL2 in root directory for more details
*/

/*
   Created for Unicorn Engine by <PERSON> <<EMAIL>>, 2022
   Copyright 2022 Aptiv
*/

#ifndef UNICORN_TRICORE_H
#define UNICORN_TRICORE_H

#ifdef __cplusplus
extern "C" {
#endif

#ifdef _MSC_VER
#pragma warning(disable : 4201)
#endif

//> TRICORE CPU
typedef enum uc_cpu_tricore {
    UC_CPU_TRICORE_TC1796,
    UC_CPU_TRICORE_TC1797,
    UC_CPU_TRICORE_TC27X,

    UC_CPU_TRICORE_ENDING
} uc_cpu_tricore;

//> TRICORE registers
typedef enum uc_tricore_reg {
    UC_TRICORE_REG_INVALID = 0,

    // General purpose registers (GPR)
    // Address GPR
    UC_TRICORE_REG_A0,
    UC_TRICORE_REG_A1,
    UC_TRICORE_REG_A2,
    UC_TRICORE_REG_A3,
    UC_TRICORE_REG_A4,
    UC_TRICORE_REG_A5,
    UC_TRICORE_REG_A6,
    UC_TRICORE_REG_A7,
    UC_TRICORE_REG_A8,
    UC_TRICORE_REG_A9,
    UC_TRICORE_REG_A10,
    UC_TRICORE_REG_A11,
    UC_TRICORE_REG_A12,
    UC_TRICORE_REG_A13,
    UC_TRICORE_REG_A14,
    UC_TRICORE_REG_A15,
    // Data GPR
    UC_TRICORE_REG_D0,
    UC_TRICORE_REG_D1,
    UC_TRICORE_REG_D2,
    UC_TRICORE_REG_D3,
    UC_TRICORE_REG_D4,
    UC_TRICORE_REG_D5,
    UC_TRICORE_REG_D6,
    UC_TRICORE_REG_D7,
    UC_TRICORE_REG_D8,
    UC_TRICORE_REG_D9,
    UC_TRICORE_REG_D10,
    UC_TRICORE_REG_D11,
    UC_TRICORE_REG_D12,
    UC_TRICORE_REG_D13,
    UC_TRICORE_REG_D14,
    UC_TRICORE_REG_D15,

    /* CSFR Register */
    UC_TRICORE_REG_PCXI,

    UC_TRICORE_REG_PSW,

    /* PSW flag cache for faster execution */
    UC_TRICORE_REG_PSW_USB_C,
    UC_TRICORE_REG_PSW_USB_V,
    UC_TRICORE_REG_PSW_USB_SV,
    UC_TRICORE_REG_PSW_USB_AV,
    UC_TRICORE_REG_PSW_USB_SAV,

    UC_TRICORE_REG_PC,
    UC_TRICORE_REG_SYSCON,
    UC_TRICORE_REG_CPU_ID,
    UC_TRICORE_REG_BIV,
    UC_TRICORE_REG_BTV,
    UC_TRICORE_REG_ISP,
    UC_TRICORE_REG_ICR,
    UC_TRICORE_REG_FCX,
    UC_TRICORE_REG_LCX,
    UC_TRICORE_REG_COMPAT,

    UC_TRICORE_REG_DPR0_U,
    UC_TRICORE_REG_DPR1_U,
    UC_TRICORE_REG_DPR2_U,
    UC_TRICORE_REG_DPR3_U,
    UC_TRICORE_REG_DPR0_L,
    UC_TRICORE_REG_DPR1_L,
    UC_TRICORE_REG_DPR2_L,
    UC_TRICORE_REG_DPR3_L,

    UC_TRICORE_REG_CPR0_U,
    UC_TRICORE_REG_CPR1_U,
    UC_TRICORE_REG_CPR2_U,
    UC_TRICORE_REG_CPR3_U,
    UC_TRICORE_REG_CPR0_L,
    UC_TRICORE_REG_CPR1_L,
    UC_TRICORE_REG_CPR2_L,
    UC_TRICORE_REG_CPR3_L,

    UC_TRICORE_REG_DPM0,
    UC_TRICORE_REG_DPM1,
    UC_TRICORE_REG_DPM2,
    UC_TRICORE_REG_DPM3,

    UC_TRICORE_REG_CPM0,
    UC_TRICORE_REG_CPM1,
    UC_TRICORE_REG_CPM2,
    UC_TRICORE_REG_CPM3,

    /* Memory Management Registers */
    UC_TRICORE_REG_MMU_CON,
    UC_TRICORE_REG_MMU_ASI,
    UC_TRICORE_REG_MMU_TVA,
    UC_TRICORE_REG_MMU_TPA,
    UC_TRICORE_REG_MMU_TPX,
    UC_TRICORE_REG_MMU_TFA,

    // 1.3.1 Only
    UC_TRICORE_REG_BMACON,
    UC_TRICORE_REG_SMACON,
    UC_TRICORE_REG_DIEAR,
    UC_TRICORE_REG_DIETR,
    UC_TRICORE_REG_CCDIER,
    UC_TRICORE_REG_MIECON,
    UC_TRICORE_REG_PIEAR,
    UC_TRICORE_REG_PIETR,
    UC_TRICORE_REG_CCPIER,

    /* Debug Registers */
    UC_TRICORE_REG_DBGSR,
    UC_TRICORE_REG_EXEVT,
    UC_TRICORE_REG_CREVT,
    UC_TRICORE_REG_SWEVT,
    UC_TRICORE_REG_TR0EVT,
    UC_TRICORE_REG_TR1EVT,
    UC_TRICORE_REG_DMS,
    UC_TRICORE_REG_DCX,
    UC_TRICORE_REG_DBGTCR,
    UC_TRICORE_REG_CCTRL,
    UC_TRICORE_REG_CCNT,
    UC_TRICORE_REG_ICNT,
    UC_TRICORE_REG_M1CNT,
    UC_TRICORE_REG_M2CNT,
    UC_TRICORE_REG_M3CNT,

    UC_TRICORE_REG_ENDING, // <-- mark the end of the list of registers

    // alias registers
    UC_TRICORE_REG_GA0 = UC_TRICORE_REG_A0,
    UC_TRICORE_REG_GA1 = UC_TRICORE_REG_A1,
    UC_TRICORE_REG_GA8 = UC_TRICORE_REG_A8,
    UC_TRICORE_REG_GA9 = UC_TRICORE_REG_A9,
    UC_TRICORE_REG_SP = UC_TRICORE_REG_A10,
    UC_TRICORE_REG_LR = UC_TRICORE_REG_A11,
    UC_TRICORE_REG_IA = UC_TRICORE_REG_A15,
    UC_TRICORE_REG_ID = UC_TRICORE_REG_D15,
} uc_tricore_reg;

#ifdef __cplusplus
}
#endif

#endif