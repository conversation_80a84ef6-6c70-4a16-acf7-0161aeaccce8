import requests

# response = requests.get('https://www.baidu.com')

# print("状态码：",response.status_code)
# print("网页内容：",response.text[:100])


# #带参数的请求
# params = {'q':'python教程'}
# response = requests.get('https://www.baidu.com/s',params=params)

# print("状态码：", response.status_code)
# print("实际请求URL：", response.url)
# print("搜索结果页面：", response.text[:300])

# #发送POST请求，也就是登录
# data = {'username':'张三','password':'123456'}
# response = requests.post('http://example.com/login',data=data)


#请求头
# headers = {
#     #告诉你什么浏览器
#     'User-Agent': 'Mozilla/5.0 (Windows NT 10.0;Win64; x64)AppleWebKit/537.36',
#      #告诉服务器你从哪个页面来的   
#     'Referer': 'https://www.baidu.com',
#     #告诉服务器你接受什么类型的内容
#     'Accept': 'text/html,application/json',
#     #身份验证信息
#     'Cookie': 'session_id=abc123'
# }



'''
headers = {
    #告诉你什么浏览器
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0;Win64; x64)AppleWebKit/537.36',
     #告诉服务器你从哪个页面来的   
    'Referer': 'https://www.baidu.com',
    #告诉服务器你接受什么类型的内容
    'Accept': 'text/html,application/json',
    #身份验证信息
    'Cookie': 'session_id=abc123'
}
params = {'q':'python教程'}

response = requests.get('https://www.baidu.com/s',params=params,headers=headers)
print("状态码：", response.status_code)
print("实际请求URL：", response.url)
    # 添加这些来查看结果
print("响应内容长度：", len(response.text))
print("网页内容预览：", response.text[:500])  # 前500个字符

上面的不行，下面的才可以

headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',  # 完整版本
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',  # 真实浏览器的Accept
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',  # 语言偏好
    'Accept-Encoding': 'gzip, deflate, br',  # 支持的编码
    'Connection': 'keep-alive',  # 连接方式
    'Upgrade-Insecure-Requests': '1',  # 安全请求
    'Sec-Fetch-Dest': 'document',  # Chrome安全特征
    'Sec-Fetch-Mode': 'navigate',  # Chrome安全特征
    'Sec-Fetch-Site': 'none',  # Chrome安全特征
    'Cache-Control': 'max-age=0'  # 缓存控制
}
    '''


#练习
# headers = {
#     'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
# }
# response = requests.get('https://movie.douban.com',headers=headers)
# print(f"状态码：{response.status_code}")
# print(f"页面标题：{response.text.split('<title>')[1].spilt('<title>')[0]}")


#练习1
headers = {
    'User-Agent':'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept' : 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',  # 语言偏好
    'Accept-Encoding': 'gzip, deflate, br',  # 支持的编码
    'Connection': 'keep-alive',  # 连接方式
    'Upgrade-Insecure-Requests': '1',  # 安全请求
    'Sec-Fetch-Dest': 'document',  # Chrome安全特征
    'Sec-Fetch-Mode': 'navigate',  # Chrome安全特征
    'Sec-Fetch-Site': 'none',  # Chrome安全特征
    'Cache-Control': 'max-age=0'  # 缓存控制
}
response = requests.get('https://httpbin.org/headers')

print("状态码：", response.status_code)
print("实际请求URL：", response.url)
data = response.json()
for key, value in data['headers'].items():
    print(f"  {key}: {value}")