"""
健康检查API
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from datetime import datetime
import psutil
import os

from ..database import get_db, db_manager
from ..config import get_settings

router = APIRouter()
settings = get_settings()


@router.get("/")
async def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": settings.app_name,
        "version": settings.app_version
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """详细健康检查"""
    
    # 检查数据库连接
    db_status = "healthy"
    try:
        db.execute("SELECT 1")
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    # 系统资源信息
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    return {
        "status": "healthy" if db_status == "healthy" else "unhealthy",
        "timestamp": datetime.now().isoformat(),
        "service": {
            "name": settings.app_name,
            "version": settings.app_version,
            "environment": "development" if settings.debug else "production"
        },
        "database": {
            "status": db_status,
            "url": settings.database_url.split("@")[-1] if "@" in settings.database_url else "local"
        },
        "system": {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total": memory.total,
                "available": memory.available,
                "percent": memory.percent
            },
            "disk": {
                "total": disk.total,
                "free": disk.free,
                "percent": (disk.used / disk.total) * 100
            }
        },
        "directories": {
            "downloads": os.path.exists(settings.spider_download_path),
            "uploads": os.path.exists(settings.upload_path),
            "logs": os.path.exists(os.path.dirname(settings.log_file))
        }
    }


@router.get("/database")
async def database_health(db: Session = Depends(get_db)):
    """数据库健康检查"""
    try:
        # 检查数据库连接
        result = db.execute("SELECT 1").scalar()
        
        # 检查表是否存在
        tables_exist = db_manager.check_connection()
        
        return {
            "status": "healthy",
            "connection": "ok",
            "tables": "ok" if tables_exist else "missing",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }


@router.get("/ready")
async def readiness_check(db: Session = Depends(get_db)):
    """就绪检查 - 用于Kubernetes等容器编排"""
    try:
        # 检查数据库
        db.execute("SELECT 1")
        
        # 检查必要目录
        required_dirs = [
            settings.spider_download_path,
            settings.upload_path
        ]
        
        for directory in required_dirs:
            if not os.path.exists(directory):
                return {"status": "not ready", "reason": f"Directory {directory} not found"}
        
        return {"status": "ready"}
    except Exception as e:
        return {"status": "not ready", "reason": str(e)}


@router.get("/live")
async def liveness_check():
    """存活检查 - 用于Kubernetes等容器编排"""
    return {"status": "alive", "timestamp": datetime.now().isoformat()}
