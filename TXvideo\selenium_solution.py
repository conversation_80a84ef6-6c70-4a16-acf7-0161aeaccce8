#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Selenium解决方案 - 真正解决免费视频爬取问题
"""

import time
import json
import re
import os
from typing import Optional, List

def install_selenium():
    """安装Selenium和ChromeDriver"""
    print("📦 检查Selenium环境...")
    
    try:
        import selenium
        print("✅ Selenium已安装")
    except ImportError:
        print("📥 安装Selenium...")
        os.system("pip install selenium")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        print("✅ Selenium导入成功")
        return True
    except Exception as e:
        print(f"❌ Selenium环境问题: {e}")
        print("💡 请手动安装: pip install selenium")
        print("💡 并下载ChromeDriver: https://chromedriver.chromium.org/")
        return False

class SeleniumVideoExtractor:
    """使用Selenium提取视频的真实下载链接"""
    
    def __init__(self, headless=True):
        self.headless = headless
        self.driver = None
        self._setup_driver()
    
    def _setup_driver(self):
        """设置Chrome驱动"""
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            options = Options()
            if self.headless:
                options.add_argument('--headless')
            
            # 添加更多选项以避免检测
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_experimental_option("excludeSwitches", ["enable-automation"])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 设置User-Agent
            options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
            
            self.driver = webdriver.Chrome(options=options)
            
            # 执行脚本以避免检测
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("✅ Chrome驱动初始化成功")
            
        except Exception as e:
            print(f"❌ Chrome驱动初始化失败: {e}")
            print("💡 请确保已安装ChromeDriver")
            self.driver = None
    
    def extract_video_urls(self, video_url: str) -> List[str]:
        """提取视频的真实下载链接"""
        if not self.driver:
            print("❌ 驱动未初始化")
            return []
        
        try:
            print(f"🌐 访问视频页面: {video_url}")
            self.driver.get(video_url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 查找播放按钮并点击
            self._try_play_video()
            
            # 监听网络请求
            video_urls = self._capture_video_requests()
            
            return video_urls
            
        except Exception as e:
            print(f"❌ 提取失败: {e}")
            return []
    
    def _try_play_video(self):
        """尝试播放视频"""
        try:
            from selenium.webdriver.common.by import By
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC
            
            # 等待播放器加载
            wait = WebDriverWait(self.driver, 10)
            
            # 尝试多种播放按钮选择器
            play_selectors = [
                '.txp_btn_play',
                '.txp-btn-play',
                '.play-btn',
                '.video-play-btn',
                '[class*="play"]',
                'button[title*="播放"]',
            ]
            
            for selector in play_selectors:
                try:
                    play_button = wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                    play_button.click()
                    print("✅ 点击播放按钮成功")
                    time.sleep(3)
                    break
                except:
                    continue
            
            # 如果没找到播放按钮，尝试点击视频区域
            try:
                video_area = self.driver.find_element(By.CSS_SELECTOR, 'video, .txp-video, .video-container')
                video_area.click()
                print("✅ 点击视频区域")
                time.sleep(3)
            except:
                print("⚠️ 未找到可点击的播放元素")
            
        except Exception as e:
            print(f"⚠️ 播放视频失败: {e}")
    
    def _capture_video_requests(self) -> List[str]:
        """捕获视频请求"""
        video_urls = []
        
        try:
            # 方法1: 从页面源码中提取
            page_source = self.driver.page_source
            
            # 查找m3u8链接
            m3u8_pattern = r'https?://[^"\']*\.m3u8[^"\']*'
            m3u8_urls = re.findall(m3u8_pattern, page_source)
            
            for url in m3u8_urls:
                if url not in video_urls:
                    video_urls.append(url)
                    print(f"🎬 找到m3u8: {url[:80]}...")
            
            # 方法2: 从JavaScript变量中提取
            js_video_urls = self._extract_from_js_variables()
            video_urls.extend(js_video_urls)
            
            # 方法3: 执行JavaScript获取视频元素
            js_element_urls = self._extract_from_video_elements()
            video_urls.extend(js_element_urls)
            
        except Exception as e:
            print(f"⚠️ 捕获请求失败: {e}")
        
        return list(set(video_urls))  # 去重
    
    def _extract_from_js_variables(self) -> List[str]:
        """从JavaScript变量中提取视频URL"""
        urls = []
        
        try:
            # 执行JavaScript获取可能的视频URL
            js_code = """
            var urls = [];
            
            // 查找常见的视频URL变量
            if (window.VIDEO_INFO) urls.push(JSON.stringify(window.VIDEO_INFO));
            if (window.__INITIAL_STATE__) urls.push(JSON.stringify(window.__INITIAL_STATE__));
            if (window.PAGE_INFO) urls.push(JSON.stringify(window.PAGE_INFO));
            
            // 查找video元素的src
            var videos = document.querySelectorAll('video');
            for (var i = 0; i < videos.length; i++) {
                if (videos[i].src) urls.push(videos[i].src);
                if (videos[i].currentSrc) urls.push(videos[i].currentSrc);
            }
            
            return urls;
            """
            
            result = self.driver.execute_script(js_code)
            
            if result:
                for item in result:
                    # 从JSON字符串中提取URL
                    m3u8_matches = re.findall(r'https?://[^"\']*\.m3u8[^"\']*', str(item))
                    urls.extend(m3u8_matches)
                    
                    mp4_matches = re.findall(r'https?://[^"\']*\.mp4[^"\']*', str(item))
                    urls.extend(mp4_matches)
            
        except Exception as e:
            print(f"⚠️ JavaScript提取失败: {e}")
        
        return urls
    
    def _extract_from_video_elements(self) -> List[str]:
        """从video元素中提取URL"""
        urls = []
        
        try:
            from selenium.webdriver.common.by import By
            
            # 查找所有video元素
            video_elements = self.driver.find_elements(By.TAG_NAME, 'video')
            
            for video in video_elements:
                try:
                    src = video.get_attribute('src')
                    if src:
                        urls.append(src)
                        print(f"🎬 video元素src: {src[:80]}...")
                    
                    current_src = video.get_attribute('currentSrc')
                    if current_src and current_src != src:
                        urls.append(current_src)
                        print(f"🎬 video元素currentSrc: {current_src[:80]}...")
                        
                except:
                    continue
            
        except Exception as e:
            print(f"⚠️ video元素提取失败: {e}")
        
        return urls
    
    def download_video(self, video_url: str, output_path: str) -> bool:
        """下载视频"""
        video_urls = self.extract_video_urls(video_url)
        
        if not video_urls:
            print("❌ 未找到视频下载链接")
            return False
        
        print(f"🎉 找到 {len(video_urls)} 个视频链接")
        
        # 使用第一个链接下载
        download_url = video_urls[0]
        print(f"📥 开始下载: {download_url}")
        
        try:
            import requests
            
            response = requests.get(download_url, stream=True, timeout=30)
            if response.status_code == 200:
                with open(output_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=1024*1024):
                        if chunk:
                            f.write(chunk)
                
                print(f"✅ 下载完成: {output_path}")
                return True
            else:
                print(f"❌ 下载失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 下载异常: {e}")
            return False
    
    def close(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("✅ 浏览器已关闭")

def main():
    """主函数"""
    print("🚀 Selenium视频提取解决方案")
    print("=" * 50)
    
    # 检查环境
    if not install_selenium():
        return
    
    # 测试视频
    test_url = "https://v.qq.com/x/cover/qwzpd184if155sa/p00156giz3p.html"
    output_file = "selenium_video.mp4"
    
    print(f"🎯 测试视频: {test_url}")
    
    # 创建提取器
    extractor = SeleniumVideoExtractor(headless=False)  # 显示浏览器以便调试
    
    try:
        # 提取并下载
        success = extractor.download_video(test_url, output_file)
        
        if success:
            print("🎉 Selenium方案成功！")
        else:
            print("❌ Selenium方案失败")
            print("💡 可能需要:")
            print("1. 手动处理验证码或登录")
            print("2. 调整选择器")
            print("3. 增加等待时间")
            
    finally:
        extractor.close()

if __name__ == "__main__":
    main()
