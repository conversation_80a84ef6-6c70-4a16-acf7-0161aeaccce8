# For Unicorn Engine. AUTO-GENERATED FILE, DO NOT EDIT [arm_const.py]

# ARM CPU

UC_CPU_ARM_926 = 0
UC_CPU_ARM_946 = 1
UC_CPU_ARM_1026 = 2
UC_CPU_ARM_1136_R2 = 3
UC_CPU_ARM_1136 = 4
UC_CPU_ARM_1176 = 5
UC_CPU_ARM_11MPCORE = 6
UC_CPU_ARM_CORTEX_M0 = 7
UC_CPU_ARM_CORTEX_M3 = 8
UC_CPU_ARM_CORTEX_M4 = 9
UC_CPU_ARM_CORTEX_M7 = 10
UC_CPU_ARM_CORTEX_M33 = 11
UC_CPU_ARM_CORTEX_R5 = 12
UC_CPU_ARM_CORTEX_R5F = 13
UC_CPU_ARM_CORTEX_A7 = 14
UC_CPU_ARM_CORTEX_A8 = 15
UC_CPU_ARM_CORTEX_A9 = 16
UC_CPU_ARM_CORTEX_A15 = 17
UC_CPU_ARM_TI925T = 18
UC_CPU_ARM_SA1100 = 19
UC_CPU_ARM_SA1110 = 20
UC_CPU_ARM_PXA250 = 21
UC_CPU_ARM_PXA255 = 22
UC_CPU_ARM_PXA260 = 23
UC_CPU_ARM_PXA261 = 24
UC_CPU_ARM_PXA262 = 25
UC_CPU_ARM_PXA270 = 26
UC_CPU_ARM_PXA270A0 = 27
UC_CPU_ARM_PXA270A1 = 28
UC_CPU_ARM_PXA270B0 = 29
UC_CPU_ARM_PXA270B1 = 30
UC_CPU_ARM_PXA270C0 = 31
UC_CPU_ARM_PXA270C5 = 32
UC_CPU_ARM_MAX = 33
UC_CPU_ARM_ENDING = 34

# ARM registers

UC_ARM_REG_INVALID = 0
UC_ARM_REG_APSR = 1
UC_ARM_REG_APSR_NZCV = 2
UC_ARM_REG_CPSR = 3
UC_ARM_REG_FPEXC = 4
UC_ARM_REG_FPINST = 5
UC_ARM_REG_FPSCR = 6
UC_ARM_REG_FPSCR_NZCV = 7
UC_ARM_REG_FPSID = 8
UC_ARM_REG_ITSTATE = 9
UC_ARM_REG_LR = 10
UC_ARM_REG_PC = 11
UC_ARM_REG_SP = 12
UC_ARM_REG_SPSR = 13
UC_ARM_REG_D0 = 14
UC_ARM_REG_D1 = 15
UC_ARM_REG_D2 = 16
UC_ARM_REG_D3 = 17
UC_ARM_REG_D4 = 18
UC_ARM_REG_D5 = 19
UC_ARM_REG_D6 = 20
UC_ARM_REG_D7 = 21
UC_ARM_REG_D8 = 22
UC_ARM_REG_D9 = 23
UC_ARM_REG_D10 = 24
UC_ARM_REG_D11 = 25
UC_ARM_REG_D12 = 26
UC_ARM_REG_D13 = 27
UC_ARM_REG_D14 = 28
UC_ARM_REG_D15 = 29
UC_ARM_REG_D16 = 30
UC_ARM_REG_D17 = 31
UC_ARM_REG_D18 = 32
UC_ARM_REG_D19 = 33
UC_ARM_REG_D20 = 34
UC_ARM_REG_D21 = 35
UC_ARM_REG_D22 = 36
UC_ARM_REG_D23 = 37
UC_ARM_REG_D24 = 38
UC_ARM_REG_D25 = 39
UC_ARM_REG_D26 = 40
UC_ARM_REG_D27 = 41
UC_ARM_REG_D28 = 42
UC_ARM_REG_D29 = 43
UC_ARM_REG_D30 = 44
UC_ARM_REG_D31 = 45
UC_ARM_REG_FPINST2 = 46
UC_ARM_REG_MVFR0 = 47
UC_ARM_REG_MVFR1 = 48
UC_ARM_REG_MVFR2 = 49
UC_ARM_REG_Q0 = 50
UC_ARM_REG_Q1 = 51
UC_ARM_REG_Q2 = 52
UC_ARM_REG_Q3 = 53
UC_ARM_REG_Q4 = 54
UC_ARM_REG_Q5 = 55
UC_ARM_REG_Q6 = 56
UC_ARM_REG_Q7 = 57
UC_ARM_REG_Q8 = 58
UC_ARM_REG_Q9 = 59
UC_ARM_REG_Q10 = 60
UC_ARM_REG_Q11 = 61
UC_ARM_REG_Q12 = 62
UC_ARM_REG_Q13 = 63
UC_ARM_REG_Q14 = 64
UC_ARM_REG_Q15 = 65
UC_ARM_REG_R0 = 66
UC_ARM_REG_R1 = 67
UC_ARM_REG_R2 = 68
UC_ARM_REG_R3 = 69
UC_ARM_REG_R4 = 70
UC_ARM_REG_R5 = 71
UC_ARM_REG_R6 = 72
UC_ARM_REG_R7 = 73
UC_ARM_REG_R8 = 74
UC_ARM_REG_R9 = 75
UC_ARM_REG_R10 = 76
UC_ARM_REG_R11 = 77
UC_ARM_REG_R12 = 78
UC_ARM_REG_S0 = 79
UC_ARM_REG_S1 = 80
UC_ARM_REG_S2 = 81
UC_ARM_REG_S3 = 82
UC_ARM_REG_S4 = 83
UC_ARM_REG_S5 = 84
UC_ARM_REG_S6 = 85
UC_ARM_REG_S7 = 86
UC_ARM_REG_S8 = 87
UC_ARM_REG_S9 = 88
UC_ARM_REG_S10 = 89
UC_ARM_REG_S11 = 90
UC_ARM_REG_S12 = 91
UC_ARM_REG_S13 = 92
UC_ARM_REG_S14 = 93
UC_ARM_REG_S15 = 94
UC_ARM_REG_S16 = 95
UC_ARM_REG_S17 = 96
UC_ARM_REG_S18 = 97
UC_ARM_REG_S19 = 98
UC_ARM_REG_S20 = 99
UC_ARM_REG_S21 = 100
UC_ARM_REG_S22 = 101
UC_ARM_REG_S23 = 102
UC_ARM_REG_S24 = 103
UC_ARM_REG_S25 = 104
UC_ARM_REG_S26 = 105
UC_ARM_REG_S27 = 106
UC_ARM_REG_S28 = 107
UC_ARM_REG_S29 = 108
UC_ARM_REG_S30 = 109
UC_ARM_REG_S31 = 110
UC_ARM_REG_C1_C0_2 = 111
UC_ARM_REG_C13_C0_2 = 112
UC_ARM_REG_C13_C0_3 = 113
UC_ARM_REG_IPSR = 114
UC_ARM_REG_MSP = 115
UC_ARM_REG_PSP = 116
UC_ARM_REG_CONTROL = 117
UC_ARM_REG_IAPSR = 118
UC_ARM_REG_EAPSR = 119
UC_ARM_REG_XPSR = 120
UC_ARM_REG_EPSR = 121
UC_ARM_REG_IEPSR = 122
UC_ARM_REG_PRIMASK = 123
UC_ARM_REG_BASEPRI = 124
UC_ARM_REG_BASEPRI_MAX = 125
UC_ARM_REG_FAULTMASK = 126
UC_ARM_REG_APSR_NZCVQ = 127
UC_ARM_REG_APSR_G = 128
UC_ARM_REG_APSR_NZCVQG = 129
UC_ARM_REG_IAPSR_NZCVQ = 130
UC_ARM_REG_IAPSR_G = 131
UC_ARM_REG_IAPSR_NZCVQG = 132
UC_ARM_REG_EAPSR_NZCVQ = 133
UC_ARM_REG_EAPSR_G = 134
UC_ARM_REG_EAPSR_NZCVQG = 135
UC_ARM_REG_XPSR_NZCVQ = 136
UC_ARM_REG_XPSR_G = 137
UC_ARM_REG_XPSR_NZCVQG = 138
UC_ARM_REG_CP_REG = 139
UC_ARM_REG_ENDING = 140

# alias registers
UC_ARM_REG_R13 = 12
UC_ARM_REG_R14 = 10
UC_ARM_REG_R15 = 11
UC_ARM_REG_SB = 75
UC_ARM_REG_SL = 76
UC_ARM_REG_FP = 77
UC_ARM_REG_IP = 78
