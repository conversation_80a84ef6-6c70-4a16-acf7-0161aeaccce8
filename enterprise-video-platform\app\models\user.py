"""
用户数据模型
"""

from sqlalchemy import Column, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from .base import BaseModel


class User(BaseModel):
    """用户模型"""
    
    __tablename__ = "users"
    
    # 基本信息
    username = Column(String(50), unique=True, index=True, nullable=False, comment="用户名")
    email = Column(String(100), unique=True, index=True, nullable=False, comment="邮箱")
    hashed_password = Column(String(255), nullable=False, comment="加密密码")
    
    # 用户状态
    is_active = Column(Boolean, default=True, comment="是否激活")
    is_superuser = Column(Boolean, default=False, comment="是否超级用户")
    is_verified = Column(Boolean, default=False, comment="是否验证邮箱")
    
    # 个人信息
    full_name = Column(String(100), comment="全名")
    avatar_url = Column(String(255), comment="头像URL")
    bio = Column(Text, comment="个人简介")
    
    # 最后登录时间
    last_login = Column(DateTime(timezone=True), comment="最后登录时间")
    
    # 关联关系
    download_histories = relationship(
        "DownloadHistory", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    video_collections = relationship(
        "VideoCollection", 
        back_populates="user",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"
    
    @property
    def is_admin(self):
        """是否为管理员"""
        return self.is_superuser
    
    def check_permission(self, permission: str) -> bool:
        """检查用户权限"""
        if self.is_superuser:
            return True
        # 这里可以扩展更复杂的权限逻辑
        return False
