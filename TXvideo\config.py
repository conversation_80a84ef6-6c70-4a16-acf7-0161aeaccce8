#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫配置文件
"""

# 请求头配置
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36',
    'Referer': 'https://v.qq.com/',
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
}

# API接口配置
API_ENDPOINTS = {
    'PROXY_HTTP': 'https://vd.l.qq.com/proxyhttp',
    'PLAY_INFO': 'https://playvv.yangshipin.cn/playvinfo',
    'VIDEO_INFO': 'https://vd.l.qq.com/proxyhttp'
}

# 加密相关配置
CRYPTO_CONFIG = {
    'AES_KEY': "4E2918885FD98109869D14E0231A0BF4",
    'AES_IV': "16B17E519DDD0CE5B79D7A63A4DD801C",
    'FIXED_KEY': "mg3c3b04ba"
}

# 平台配置
PLATFORM_CONFIG = {
    'PC': {
        'platform': '10201',
        'app_ver': '3.5.57',
        'ending': 'https://v.qq.com/|mozilla/5.0 (windows nt 10.0; win64; x64) applewebkit/537.36||Mozilla|Netscape|Win32|'
    },
    'MOBILE': {
        'platform': '4330701',
        'app_ver': '1.3.5',
        'ending': 'https://m.v.qq.com/|mozilla/5.0 (linux; android 6.0; nexus 5)||Mozilla|Netscape|Linux armv7l|'
    }
}

# 下载配置
DOWNLOAD_CONFIG = {
    'TIMEOUT': 30,
    'RETRY_TIMES': 3,
    'CHUNK_SIZE': 1024 * 1024,  # 1MB
    'MAX_WORKERS': 5,
    'OUTPUT_DIR': './downloads/'
}

# 日志配置
LOG_CONFIG = {
    'LEVEL': 'INFO',
    'FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'FILE': 'tx_spider.log'
}

# 代理配置（可选）
PROXY_CONFIG = {
    'ENABLE': False,
    'HTTP_PROXY': 'http://127.0.0.1:8080',
    'HTTPS_PROXY': 'https://127.0.0.1:8080'
}

# 视频质量配置
VIDEO_QUALITY = {
    'FHD': 'fhd',      # 1080P
    'HD': 'hd',        # 720P
    'SD': 'sd',        # 480P
    'LD': 'ld'         # 360P
}

# 支持的视频格式
SUPPORTED_FORMATS = ['mp4', 'm3u8', 'flv']

# 错误重试配置
RETRY_CONFIG = {
    'MAX_RETRIES': 3,
    'BACKOFF_FACTOR': 1,
    'STATUS_FORCELIST': [500, 502, 503, 504]
}
