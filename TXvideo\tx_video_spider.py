#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫基础框架
作者: AI Assistant
创建时间: 2025-01-30
功能: 爬取腾讯视频的基础框架，包含参数提取和视频下载
注意: 仅供学习研究使用，请遵守相关法律法规
"""

import re
import time
import json
import hashlib
import binascii
import requests
import execjs
from urllib.parse import urlparse, parse_qs, urlencode
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from typing import Dict, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TencentVideoSpider:
    """腾讯视频爬虫主类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Referer': 'https://v.qq.com/',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'sec-ch-ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
        })
        
        # 加密相关常量
        self.KEY = binascii.a2b_hex("4E2918885FD98109869D14E0231A0BF4")
        self.IV = binascii.a2b_hex("16B17E519DDD0CE5B79D7A63A4DD801C")
        
        # JavaScript执行环境
        self.js_context = execjs.compile('''
            function createGUID() {
                var e = (new Date).getTime().toString(36),
                    t = Math.random().toString(36).replace(/^0./, "");
                return "".concat(e, "_").concat(t)
            }
            
            function generateRandomString(length) {
                var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                var result = '';
                for (var i = 0; i < length; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
        ''')

    def extract_video_info(self, video_url: str) -> Dict[str, str]:
        """从视频URL中提取基本信息"""
        try:
            vid, coverid = "", ""

            # 解析URL获取vid和coverid
            if '/x/cover/' in video_url:
                # 格式1: https://v.qq.com/x/cover/coverid/vid.html
                match = re.search(r'/x/cover/([^/]+)/([^/\.]+)', video_url)
                if match:
                    coverid = match.group(1)
                    vid = match.group(2)
                    logger.info(f"格式1匹配: coverid={coverid}, vid={vid}")
                else:
                    # 格式2: https://v.qq.com/x/cover/single_id.html
                    match = re.search(r'/x/cover/([^/\.]+)\.html', video_url)
                    if match:
                        single_id = match.group(1)
                        # 对于单ID格式，通常ID既是vid也是coverid
                        vid = single_id
                        coverid = single_id
                        logger.info(f"格式2匹配: single_id={single_id}")
                    else:
                        # 尝试从查询参数中获取
                        parsed_url = urlparse(video_url)
                        query_params = parse_qs(parsed_url.query)
                        vid = query_params.get('vid', [''])[0]
                        coverid = query_params.get('coverid', [''])[0]
                        logger.info(f"查询参数提取: vid={vid}, coverid={coverid}")
            else:
                # 其他格式的URL处理
                parsed_url = urlparse(video_url)
                query_params = parse_qs(parsed_url.query)
                vid = query_params.get('vid', [''])[0]
                coverid = query_params.get('coverid', [''])[0]
                logger.info(f"其他格式提取: vid={vid}, coverid={coverid}")

            # 如果仍然没有找到，尝试从页面内容中提取
            if not vid:
                logger.info("尝试从页面内容中提取vid和coverid...")
                vid, coverid = self._extract_from_page(video_url)

            if not vid:
                logger.error("无法从URL中提取vid")
                return {}

            return {
                'vid': vid,
                'coverid': coverid or vid,  # 如果没有coverid，使用vid
                'url': video_url
            }

        except Exception as e:
            logger.error(f"提取视频信息失败: {e}")
            return {}

    def _extract_from_page(self, video_url: str) -> Tuple[str, str]:
        """从页面内容中提取vid和coverid"""
        try:
            response = self.session.get(video_url, timeout=10)
            content = response.text

            # 查找vid的多种模式
            vid_patterns = [
                r'"vid"\s*:\s*"([^"]+)"',
                r'vid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'data-vid["\']?\s*=\s*["\']([^"\']+)["\']',
                r'vid=([^&\s]+)',
            ]

            # 查找coverid的多种模式
            coverid_patterns = [
                r'"cover_id"\s*:\s*"([^"]+)"',
                r'"coverid"\s*:\s*"([^"]+)"',
                r'coverid["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'data-coverid["\']?\s*=\s*["\']([^"\']+)["\']',
            ]

            vid = ""
            for pattern in vid_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    vid = match.group(1)
                    logger.info(f"从页面提取vid成功: {vid}")
                    break

            coverid = ""
            for pattern in coverid_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    coverid = match.group(1)
                    logger.info(f"从页面提取coverid成功: {coverid}")
                    break

            return vid, coverid or vid

        except Exception as e:
            logger.error(f"从页面提取信息失败: {e}")
            return "", ""

    def aes_encrypt(self, data_string: str) -> str:
        """AES加密"""
        try:
            aes = AES.new(key=self.KEY, mode=AES.MODE_CBC, iv=self.IV)
            raw = pad(data_string.encode('utf-8'), 16)
            aes_bytes = aes.encrypt(raw)
            return binascii.b2a_hex(aes_bytes).decode().upper()
        except Exception as e:
            logger.error(f"AES加密失败: {e}")
            return ""

    def create_qa(self, data_string: str) -> int:
        """生成qa值的算法"""
        try:
            a = 0
            for char in data_string:
                char_code = ord(char)
                a = (a << 5) - a + char_code
                a &= 0xFFFFFFFF  # 保持32位整数范围
            # 转换为有符号32位整数
            if a >= 0x80000000:
                a -= 0x100000000
            return a
        except Exception as e:
            logger.error(f"生成qa值失败: {e}")
            return 0

    def create_ckey(self, vid: str, _rnd: str, app_ver: str, guid: str, platform: str) -> str:
        """生成cKey的核心算法"""
        try:
            # 1. 拼接字符串
            Wt = "mg3c3b04ba"  # 固定密钥
            ending = "https://v.qq.com/|mozilla/5.0 (windows nt 10.0; win64; x64) applewebkit/537.36||Mozilla|Netscape|Win32|"
            data_list = ["", vid, _rnd, Wt, app_ver, guid, platform, ending]
            data_string = "|".join(data_list)
            
            # 2. 生成qa值
            qa = self.create_qa(data_string)
            encrypt_string = f"|{qa}{data_string}"
            
            # 3. AES加密
            encrypted = self.aes_encrypt(encrypt_string)
            ckey = "--01" + encrypted
            
            logger.info(f"生成cKey成功: {ckey[:50]}...")
            return ckey
            
        except Exception as e:
            logger.error(f"生成cKey失败: {e}")
            return ""

    def generate_params(self, vid: str, coverid: str) -> Dict[str, str]:
        """生成请求所需的所有参数"""
        try:
            # 基础参数
            app_ver = "3.5.57"
            platform = "10201"
            guid = self.js_context.call('createGUID')
            flowid = f"{guid}_{platform}"
            _rnd = str(int(time.time()))
            
            # 生成cKey
            ckey = self.create_ckey(vid, _rnd, app_ver, guid, platform)
            
            return {
                'vid': vid,
                'coverid': coverid,
                'ckey': ckey,
                'guid': guid,
                'platform': platform,
                'app_ver': app_ver,
                'flowid': flowid,
                '_rnd': _rnd
            }
            
        except Exception as e:
            logger.error(f"生成参数失败: {e}")
            return {}

    def get_video_info(self, params: Dict[str, str]) -> Optional[Dict]:
        """获取视频信息"""
        try:
            # 构建请求数据
            buid_data = {
                "buid": "vinfoad",
                "adparam": f"pf=in&ad_type=LD|KB|PVL&pf_ex=pc&url=https://v.qq.com/x/cover/{params['coverid']}.html&refer=https://v.qq.com/&ty=web&plugin=1.0.0&coverid={params['coverid']}&vid={params['vid']}&pt=&flowid={params['flowid']}&vptag=www_baidu_com|video:poster_tle&pu=-1&chid=0&adaptor=2&dtype=1&live=0&resp_type=json&guid={params['guid']}&req_type=1&from=0&appversion={params['app_ver']}&uid=0&tkn=&lt=qq&platform={params['platform']}&opid=&atkn=&appid=101483052&tpid=1&rfid=&cKey={params['ckey']}",
                "vinfoparam": f"spsrt=1&charge=0&defaultfmt=auto&otype=ojson&guid={params['guid']}&flowid={params['flowid']}&platform={params['platform']}&sdtfrom=v1010&defnpayver=1&appVer={params['app_ver']}&host=v.qq.com&ehost=https://v.qq.com/x/cover/{params['coverid']}.html&refer=v.qq.com&sphttps=1&tm={params['_rnd']}&spwm=4&vid={params['vid']}&defn=fhd&fhdswitch=0&show1080p=1&isHLS=1&dtype=3&sphls=2&spgzip=1&dlver=2&drm=32&hdcp=0&spau=1&spaudio=15&defsrc=2&encryptVer=9.1&cKey={params['ckey']}&fp2p=1&spadseg=3"
            }

            # 发送请求
            url = "https://vd.l.qq.com/proxyhttp"
            response = self.session.post(url, json=buid_data, timeout=30)

            if response.status_code == 200:
                result = response.json()

                # 检查是否有错误信息
                vinfo = result.get('vinfo', '')
                if isinstance(vinfo, str):
                    try:
                        vinfo_dict = json.loads(vinfo)
                        if vinfo_dict.get('msg'):
                            logger.error(f"腾讯视频返回错误: {vinfo_dict.get('msg')}")
                            logger.error(f"错误代码: {vinfo_dict.get('code', 'N/A')}")

                            # 提供解决建议
                            error_msg = vinfo_dict.get('msg', '')
                            if '外星人劫走' in error_msg or '暂时看不了' in error_msg:
                                logger.error("可能的原因:")
                                logger.error("1. 视频需要VIP权限")
                                logger.error("2. 地区访问限制")
                                logger.error("3. 视频已下架或不存在")
                                logger.error("4. cKey生成算法需要更新")
                    except json.JSONDecodeError:
                        pass

                logger.info("获取视频信息成功")
                return result
            else:
                logger.error(f"请求失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取视频信息失败: {e}")
            return None

    def parse_video_urls(self, video_info: Dict) -> list:
        """解析视频URL"""
        try:
            video_urls = []

            # 调试：打印完整的响应数据
            logger.info("=== 调试信息：完整响应数据 ===")
            logger.info(f"响应键: {list(video_info.keys())}")

            # 尝试多种可能的数据结构
            possible_keys = ['vinfo', 'data', 'result', 'videoInfo', 'playInfo']

            for key in possible_keys:
                if key in video_info:
                    logger.info(f"找到键 '{key}': {str(video_info[key])[:500]}...")

            # 解析vinfo中的视频信息
            vinfo = video_info.get('vinfo', '')
            if vinfo:
                logger.info(f"vinfo内容长度: {len(vinfo)}")
                logger.info(f"vinfo前500字符: {vinfo[:500]}")

                # 尝试多种URL模式
                patterns = [
                    r'"url":"([^"]*\.m3u8[^"]*)"',  # 原始模式
                    r'"playurl":"([^"]*\.m3u8[^"]*)"',  # 播放URL
                    r'"src":"([^"]*\.m3u8[^"]*)"',  # 源URL
                    r'https?://[^"]*\.m3u8[^"]*',  # 直接匹配m3u8 URL
                    r'"url":"([^"]*)"',  # 任何URL
                ]

                for i, pattern in enumerate(patterns):
                    matches = re.findall(pattern, vinfo)
                    logger.info(f"模式 {i+1} 匹配到 {len(matches)} 个结果")

                    if matches:
                        for match in matches:
                            # 解码URL中的转义字符
                            url = match.replace('\\/', '/')
                            if '.m3u8' in url or 'video' in url.lower():
                                video_urls.append(url)
                                logger.info(f"找到视频URL: {url[:100]}...")

            # 如果vinfo为空，尝试其他字段
            if not vinfo:
                logger.info("vinfo为空，尝试其他字段...")
                # 尝试解析整个响应
                response_str = json.dumps(video_info)
                m3u8_matches = re.findall(r'https?://[^"]*\.m3u8[^"]*', response_str)
                for match in m3u8_matches:
                    video_urls.append(match)
                    logger.info(f"从完整响应中找到URL: {match[:100]}...")

            logger.info(f"解析到 {len(video_urls)} 个视频URL")
            return video_urls

        except Exception as e:
            logger.error(f"解析视频URL失败: {e}")
            return []

    def download_video(self, m3u8_url: str, output_path: str = None) -> bool:
        """下载视频（基础实现）"""
        try:
            if not output_path:
                output_path = f"video_{int(time.time())}.mp4"
            
            logger.info(f"开始下载视频: {m3u8_url}")
            
            # 获取m3u8文件内容
            response = self.session.get(m3u8_url, timeout=30)
            if response.status_code != 200:
                logger.error(f"获取m3u8文件失败: {response.status_code}")
                return False
            
            m3u8_content = response.text
            logger.info("获取m3u8文件成功")
            
            # 解析ts文件列表
            ts_urls = []
            base_url = m3u8_url.rsplit('/', 1)[0] + '/'
            
            for line in m3u8_content.split('\n'):
                line = line.strip()
                if line and not line.startswith('#'):
                    if line.startswith('http'):
                        ts_urls.append(line)
                    else:
                        ts_urls.append(base_url + line)
            
            logger.info(f"找到 {len(ts_urls)} 个视频片段")
            
            # 下载并合并ts文件
            with open(output_path, 'wb') as output_file:
                for i, ts_url in enumerate(ts_urls):
                    try:
                        ts_response = self.session.get(ts_url, timeout=30)
                        if ts_response.status_code == 200:
                            output_file.write(ts_response.content)
                            logger.info(f"下载进度: {i+1}/{len(ts_urls)}")
                        else:
                            logger.warning(f"下载片段失败: {ts_url}")
                    except Exception as e:
                        logger.warning(f"下载片段异常: {e}")
                        continue
            
            logger.info(f"视频下载完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"下载视频失败: {e}")
            return False

    def crawl_video(self, video_url: str, output_path: str = None) -> bool:
        """爬取视频的主要方法"""
        try:
            logger.info(f"开始爬取视频: {video_url}")
            
            # 1. 提取视频信息
            video_info = self.extract_video_info(video_url)
            if not video_info:
                return False
            
            # 2. 生成请求参数
            params = self.generate_params(video_info['vid'], video_info['coverid'])
            if not params:
                return False
            
            # 3. 获取视频信息
            video_data = self.get_video_info(params)
            if not video_data:
                return False
            
            # 4. 解析视频URL
            video_urls = self.parse_video_urls(video_data)
            if not video_urls:
                logger.error("未找到可用的视频URL")
                return False
            
            # 5. 下载视频（使用第一个URL）
            return self.download_video(video_urls[0], output_path)
            
        except Exception as e:
            logger.error(f"爬取视频失败: {e}")
            return False


def main():
    """主函数 - 使用示例"""
    spider = TencentVideoSpider()
    
    # 示例视频URL（请替换为实际的腾讯视频URL）
    video_url = "https://v.qq.com/x/cover/mzc00200example/example123.html"
    
    # 开始爬取
    success = spider.crawl_video(video_url, "downloaded_video.mp4")
    
    if success:
        print("视频爬取成功！")
    else:
        print("视频爬取失败！")


if __name__ == "__main__":
    main()
