# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9  # PostgreSQL
aiosqlite==0.19.0       # SQLite异步支持

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# 数据验证
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP客户端
httpx==0.25.2
aiohttp==3.9.1

# 爬虫相关
requests==2.31.0
beautifulsoup4==4.12.2
selenium==4.15.2
pycryptodome==3.19.0
PyExecJS==1.5.1

# 数据处理
pandas==2.1.4
numpy==1.25.2

# 任务队列
celery==5.3.4
redis==5.0.1

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2  # 用于测试API

# 开发工具
black==23.11.0
flake8==6.1.0
isort==5.12.0
pre-commit==3.6.0

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 环境管理
python-dotenv==1.0.0

# 文档
mkdocs==1.5.3
mkdocs-material==9.4.8
