#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫调试测试脚本
"""

from tx_video_spider import TencentVideoSpider
import json

def test_video_access():
    """测试视频访问"""
    spider = TencentVideoSpider()
    
    # 测试不同类型的视频URL
    test_urls = [
        # 你提供的URL
        "https://v.qq.com/x/cover/mzc00200km6wu5k/z0036cavinz.html?cut_vid=h4101lbswzz&scene_id=3&start=277",
        
        # 一些可能的免费视频URL（需要替换为实际存在的）
        "https://v.qq.com/x/cover/mzc00200example/example123.html",
        
        # 简化的URL（去掉查询参数）
        "https://v.qq.com/x/cover/mzc00200km6wu5k/z0036cavinz.html",
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n{'='*60}")
        print(f"测试 {i}: {url}")
        print('='*60)
        
        try:
            # 提取视频信息
            video_info = spider.extract_video_info(url)
            print(f"✅ 视频信息提取成功: vid={video_info.get('vid')}, coverid={video_info.get('coverid')}")
            
            if not video_info.get('vid'):
                print("❌ 无法提取vid，跳过")
                continue
            
            # 生成参数
            params = spider.generate_params(video_info['vid'], video_info['coverid'])
            print(f"✅ 参数生成成功")
            
            # 获取视频信息
            video_data = spider.get_video_info(params)
            
            if video_data:
                print("✅ 获取视频数据成功")
                
                # 分析响应数据
                vinfo = video_data.get('vinfo', '')
                if vinfo:
                    try:
                        if isinstance(vinfo, str):
                            vinfo_dict = json.loads(vinfo)
                        else:
                            vinfo_dict = vinfo
                            
                        if 'msg' in vinfo_dict:
                            print(f"❌ 错误信息: {vinfo_dict['msg']}")
                            print(f"   错误代码: {vinfo_dict.get('code', 'N/A')}")
                        else:
                            print("✅ 没有错误信息，可能包含视频数据")
                            print(f"   vinfo键: {list(vinfo_dict.keys())}")
                            
                    except (json.JSONDecodeError, TypeError):
                        print(f"   vinfo内容: {str(vinfo)[:200]}...")
                
                # 尝试解析视频URL
                video_urls = spider.parse_video_urls(video_data)
                if video_urls:
                    print(f"🎉 成功找到 {len(video_urls)} 个视频URL!")
                    for j, video_url in enumerate(video_urls[:3], 1):  # 只显示前3个
                        print(f"   {j}. {video_url[:100]}...")
                    return True  # 找到可用视频，返回成功
                else:
                    print("❌ 未找到视频URL")
            else:
                print("❌ 获取视频数据失败")
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    return False

def suggest_solutions():
    """提供解决方案建议"""
    print(f"\n{'='*60}")
    print("🔧 问题分析和解决建议")
    print('='*60)
    
    print("\n📋 可能的问题原因:")
    print("1. 🔒 视频需要VIP会员权限")
    print("2. 🌍 地区访问限制（IP被限制）")
    print("3. 🔑 cKey生成算法过时，需要更新")
    print("4. 🚫 反爬虫机制检测")
    print("5. 📺 视频已下架或不存在")
    
    print("\n💡 建议的解决方案:")
    print("1. 🆓 尝试免费的公开视频")
    print("2. 🔍 使用F12分析最新的请求参数")
    print("3. 🔄 更新cKey生成算法")
    print("4. 🌐 使用代理服务器")
    print("5. 🍪 添加登录Cookie")
    
    print("\n🛠️ 下一步操作:")
    print("1. 在浏览器中打开视频页面，确认能正常播放")
    print("2. 按F12打开开发者工具，查看Network请求")
    print("3. 找到proxyhttp请求，复制完整的请求参数")
    print("4. 对比框架生成的参数与实际请求的差异")
    print("5. 根据差异调整参数生成逻辑")

def main():
    """主函数"""
    print("🚀 开始腾讯视频爬虫调试测试...")
    
    success = test_video_access()
    
    if not success:
        suggest_solutions()
        
        print(f"\n{'='*60}")
        print("🔍 手动调试建议")
        print('='*60)
        print("请按以下步骤进行手动调试:")
        print("1. 打开浏览器访问: https://v.qq.com")
        print("2. 找一个免费的短视频或预告片")
        print("3. 复制视频URL，替换测试URL")
        print("4. 使用F12分析该视频的请求参数")
        print("5. 对比参数差异并调整代码")
    else:
        print("\n🎉 找到可用的视频！可以继续开发和测试。")

if __name__ == "__main__":
    main()
