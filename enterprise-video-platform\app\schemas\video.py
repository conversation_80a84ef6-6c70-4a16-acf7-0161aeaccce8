"""
视频相关Pydantic模式
"""

from pydantic import BaseModel, HttpUrl
from typing import Optional, Dict, Any
from datetime import datetime


class VideoBase(BaseModel):
    """视频基础模式"""
    title: str
    description: Optional[str] = None
    original_url: str
    vid: Optional[str] = None
    cover_id: Optional[str] = None
    cover_url: Optional[str] = None
    duration: Optional[int] = None
    quality: Optional[str] = None
    format: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[str] = None


class VideoCreate(VideoBase):
    """视频创建模式"""
    is_free: bool = False
    metadata: Optional[Dict[str, Any]] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "title": "示例视频",
                "description": "这是一个示例视频",
                "original_url": "https://v.qq.com/x/cover/example.html",
                "vid": "example123",
                "cover_id": "cover123",
                "cover_url": "https://example.com/cover.jpg",
                "duration": 3600,
                "quality": "1080p",
                "format": "mp4",
                "category": "电影",
                "tags": "动作,冒险",
                "is_free": True
            }
        }


class VideoUpdate(BaseModel):
    """视频更新模式"""
    title: Optional[str] = None
    description: Optional[str] = None
    cover_url: Optional[str] = None
    duration: Optional[int] = None
    quality: Optional[str] = None
    format: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[str] = None
    is_available: Optional[bool] = None
    is_free: Optional[bool] = None
    metadata: Optional[Dict[str, Any]] = None


class VideoResponse(VideoBase):
    """视频响应模式"""
    id: int
    file_size: Optional[int] = None
    is_available: bool
    is_free: bool
    download_count: int
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class VideoList(BaseModel):
    """视频列表响应"""
    videos: list[VideoResponse]
    total: int
    page: int
    size: int


class DownloadHistoryBase(BaseModel):
    """下载历史基础模式"""
    video_id: int
    download_url: Optional[str] = None
    file_name: Optional[str] = None


class DownloadHistoryCreate(DownloadHistoryBase):
    """下载历史创建模式"""
    pass


class DownloadHistoryResponse(DownloadHistoryBase):
    """下载历史响应模式"""
    id: int
    user_id: int
    local_path: Optional[str] = None
    status: str
    progress: float
    error_message: Optional[str] = None
    download_speed: Optional[float] = None
    downloaded_size: int
    total_size: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class VideoCollectionBase(BaseModel):
    """视频收藏基础模式"""
    video_id: int
    collection_name: str = "默认收藏"
    notes: Optional[str] = None


class VideoCollectionCreate(VideoCollectionBase):
    """视频收藏创建模式"""
    pass


class VideoCollectionResponse(VideoCollectionBase):
    """视频收藏响应模式"""
    id: int
    user_id: int
    is_favorite: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
