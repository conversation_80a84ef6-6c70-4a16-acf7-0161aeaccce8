"""
用户相关Pydantic模式
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime


class UserBase(BaseModel):
    """用户基础模式"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = None


class UserCreate(UserBase):
    """用户创建模式"""
    password: str
    
    class Config:
        json_schema_extra = {
            "example": {
                "username": "johndo<PERSON>",
                "email": "<EMAIL>",
                "password": "secretpassword",
                "full_name": "<PERSON>",
                "bio": "Video enthusiast"
            }
        }


class UserUpdate(BaseModel):
    """用户更新模式"""
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    bio: Optional[str] = None
    avatar_url: Optional[str] = None
    is_active: Optional[bool] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "full_name": "<PERSON>",
                "bio": "Updated bio",
                "avatar_url": "https://example.com/avatar.jpg"
            }
        }


class UserResponse(UserBase):
    """用户响应模式"""
    id: int
    is_active: bool
    is_superuser: bool
    is_verified: bool
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "username": "johndoe",
                "email": "<EMAIL>",
                "full_name": "John Doe",
                "bio": "Video enthusiast",
                "avatar_url": "https://example.com/avatar.jpg",
                "is_active": True,
                "is_superuser": False,
                "is_verified": True,
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z",
                "last_login": "2024-01-01T12:00:00Z"
            }
        }


class UserList(BaseModel):
    """用户列表响应"""
    users: list[UserResponse]
    total: int
    page: int
    size: int
    
    class Config:
        json_schema_extra = {
            "example": {
                "users": [],
                "total": 100,
                "page": 1,
                "size": 10
            }
        }
