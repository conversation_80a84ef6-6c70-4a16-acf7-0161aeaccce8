/* Unicorn Emulator Engine */
/* By <PERSON><PERSON><PERSON> <<EMAIL>>, 2014-2017 */
/* This file is released under LGPL2.
   See COPYING.LGPL2 in root directory for more details
*/

#ifndef UNICORN_SPARC_H
#define UNICORN_SPARC_H

#ifdef __cplusplus
extern "C" {
#endif

// GCC SPARC toolchain has a default macro called "sparc" which breaks
// compilation
#undef sparc

#ifdef _MSC_VER
#pragma warning(disable : 4201)
#endif

//> SPARC32 CPU
typedef enum uc_cpu_sparc32 {
    UC_CPU_SPARC32_FUJITSU_MB86904 = 0,
    UC_CPU_SPARC32_FUJITSU_MB86907,
    UC_CPU_SPARC32_TI_MICROSPARC_I,
    UC_CPU_SPARC32_TI_MICROSPARC_II,
    UC_CPU_SPARC32_TI_MICROSPARC_IIEP,
    UC_CPU_SPARC32_TI_SUPERSPARC_40,
    UC_CPU_SPARC32_TI_SUPERSPARC_50,
    UC_CPU_SPARC32_TI_SUPERSPARC_51,
    UC_CPU_SPARC32_TI_SUPERSPARC_60,
    UC_CPU_SPARC32_TI_SUPERSPARC_61,
    UC_CPU_SPARC32_TI_SUPERSPARC_II,
    UC_CPU_SPARC32_LEON2,
    UC_CPU_SPARC32_LEON3,

    UC_CPU_SPARC32_ENDING
} uc_cpu_sparc32;

//> SPARC64 CPU
typedef enum uc_cpu_sparc64 {
    UC_CPU_SPARC64_FUJITSU = 0,
    UC_CPU_SPARC64_FUJITSU_III,
    UC_CPU_SPARC64_FUJITSU_IV,
    UC_CPU_SPARC64_FUJITSU_V,
    UC_CPU_SPARC64_TI_ULTRASPARC_I,
    UC_CPU_SPARC64_TI_ULTRASPARC_II,
    UC_CPU_SPARC64_TI_ULTRASPARC_III,
    UC_CPU_SPARC64_TI_ULTRASPARC_IIE,
    UC_CPU_SPARC64_SUN_ULTRASPARC_III,
    UC_CPU_SPARC64_SUN_ULTRASPARC_III_CU,
    UC_CPU_SPARC64_SUN_ULTRASPARC_IIII,
    UC_CPU_SPARC64_SUN_ULTRASPARC_IV,
    UC_CPU_SPARC64_SUN_ULTRASPARC_IV_PLUS,
    UC_CPU_SPARC64_SUN_ULTRASPARC_IIII_PLUS,
    UC_CPU_SPARC64_SUN_ULTRASPARC_T1,
    UC_CPU_SPARC64_SUN_ULTRASPARC_T2,
    UC_CPU_SPARC64_NEC_ULTRASPARC_I,

    UC_CPU_SPARC64_ENDING
} uc_cpu_sparc64;

//> SPARC registers
typedef enum uc_sparc_reg {
    UC_SPARC_REG_INVALID = 0,

    UC_SPARC_REG_F0,
    UC_SPARC_REG_F1,
    UC_SPARC_REG_F2,
    UC_SPARC_REG_F3,
    UC_SPARC_REG_F4,
    UC_SPARC_REG_F5,
    UC_SPARC_REG_F6,
    UC_SPARC_REG_F7,
    UC_SPARC_REG_F8,
    UC_SPARC_REG_F9,
    UC_SPARC_REG_F10,
    UC_SPARC_REG_F11,
    UC_SPARC_REG_F12,
    UC_SPARC_REG_F13,
    UC_SPARC_REG_F14,
    UC_SPARC_REG_F15,
    UC_SPARC_REG_F16,
    UC_SPARC_REG_F17,
    UC_SPARC_REG_F18,
    UC_SPARC_REG_F19,
    UC_SPARC_REG_F20,
    UC_SPARC_REG_F21,
    UC_SPARC_REG_F22,
    UC_SPARC_REG_F23,
    UC_SPARC_REG_F24,
    UC_SPARC_REG_F25,
    UC_SPARC_REG_F26,
    UC_SPARC_REG_F27,
    UC_SPARC_REG_F28,
    UC_SPARC_REG_F29,
    UC_SPARC_REG_F30,
    UC_SPARC_REG_F31,
    UC_SPARC_REG_F32,
    UC_SPARC_REG_F34,
    UC_SPARC_REG_F36,
    UC_SPARC_REG_F38,
    UC_SPARC_REG_F40,
    UC_SPARC_REG_F42,
    UC_SPARC_REG_F44,
    UC_SPARC_REG_F46,
    UC_SPARC_REG_F48,
    UC_SPARC_REG_F50,
    UC_SPARC_REG_F52,
    UC_SPARC_REG_F54,
    UC_SPARC_REG_F56,
    UC_SPARC_REG_F58,
    UC_SPARC_REG_F60,
    UC_SPARC_REG_F62,
    UC_SPARC_REG_FCC0, // Floating condition codes
    UC_SPARC_REG_FCC1,
    UC_SPARC_REG_FCC2,
    UC_SPARC_REG_FCC3,
    UC_SPARC_REG_G0,
    UC_SPARC_REG_G1,
    UC_SPARC_REG_G2,
    UC_SPARC_REG_G3,
    UC_SPARC_REG_G4,
    UC_SPARC_REG_G5,
    UC_SPARC_REG_G6,
    UC_SPARC_REG_G7,
    UC_SPARC_REG_I0,
    UC_SPARC_REG_I1,
    UC_SPARC_REG_I2,
    UC_SPARC_REG_I3,
    UC_SPARC_REG_I4,
    UC_SPARC_REG_I5,
    UC_SPARC_REG_FP,
    UC_SPARC_REG_I7,
    UC_SPARC_REG_ICC, // Integer condition codes
    UC_SPARC_REG_L0,
    UC_SPARC_REG_L1,
    UC_SPARC_REG_L2,
    UC_SPARC_REG_L3,
    UC_SPARC_REG_L4,
    UC_SPARC_REG_L5,
    UC_SPARC_REG_L6,
    UC_SPARC_REG_L7,
    UC_SPARC_REG_O0,
    UC_SPARC_REG_O1,
    UC_SPARC_REG_O2,
    UC_SPARC_REG_O3,
    UC_SPARC_REG_O4,
    UC_SPARC_REG_O5,
    UC_SPARC_REG_SP,
    UC_SPARC_REG_O7,
    UC_SPARC_REG_Y,

    // special register
    UC_SPARC_REG_XCC,

    // pseudo register
    UC_SPARC_REG_PC, // program counter register

    UC_SPARC_REG_ENDING, // <-- mark the end of the list of registers

    // extras
    UC_SPARC_REG_O6 = UC_SPARC_REG_SP,
    UC_SPARC_REG_I6 = UC_SPARC_REG_FP,
} uc_sparc_reg;

#ifdef __cplusplus
}
#endif

#endif
