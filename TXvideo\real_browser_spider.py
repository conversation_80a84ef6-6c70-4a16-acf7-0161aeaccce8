#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实浏览器数据的腾讯视频爬虫
使用用户提供的真实请求头和Cookie
"""

from tx_video_spider import TencentVideoSpider
import json
import time

class RealBrowserSpider(TencentVideoSpider):
    """使用真实浏览器数据的爬虫"""
    
    def __init__(self):
        super().__init__()
        self._setup_real_browser_data()
    
    def _setup_real_browser_data(self):
        """设置真实的浏览器数据"""
        
        # 使用用户提供的真实请求头
        real_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6',
            'Content-Type': 'text/plain;charset=UTF-8',
            'Origin': 'https://v.qq.com',
            'Referer': 'https://v.qq.com/',
            'Sec-Ch-Ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
        }
        
        # 使用用户提供的真实Cookie
        real_cookies = {
            'qq_domain_video_guid_verify': '2b17746d6e78e452',
            '_qimei_uuid42': '1941c10192d100a844dcc561a371d0abbb5a3f7786',
            'pgv_pvid': '5749629384',
            'o_minduid': 'C_DCmQBRtjo7gTfJ-YMB3-HXWyOLorcO',
            'appuser': '6021CBDBBCFCBEB4',
            '_clck': '3990128168|1|fvh|0',
            'fqm_pvqid': '8eaa5dbd-fe36-4c17-8470-5e370b12a348',
            'RK': 'VnEQqh9bZx',
            'ptcz': '823e5f3f472c6bd658b978b77eeec7af497b5cfa77bb293305dc9b67dbd4ef7a',
            'LW_sid': 'X1n7c458y7H4l7h8q0Z8H2l8J0',
            'LW_uid': 'k187J468x724S718y0g8i228D1',
            'eas_sid': 'v1d7J4V8b7f4m748a018A7T1j0',
            'yyb_muid': '13A6BD0EB12060DB03FCA8D4B0E06170',
            '_qimei_fingerprint': 'de32a55e3d3aaf85551affceda486e4b',
            'pac_uid': '0_5fTk0rGmrdwmw',
            'pgv_info': 'ssid=s9973865320',
            'video_omgid': '2b17746d6e78e452',
            'LPDFturn': '117',
            'Lturn': '487',
            'LKBturn': '323',
            'LPVLturn': '917',
            'LPLFturn': '775',
            'LPSJturn': '939',
            'LBSturn': '344',
            'LVINturn': '484',
            'LPHLSturn': '831',
            'LDERturn': '606',
            'LZTturn': '462',
            'LPPBturn': '636'
        }
        
        # 更新会话
        self.session.headers.update(real_headers)
        self.session.cookies.update(real_cookies)
        
        print("✅ 已设置真实浏览器数据")
        print(f"   User-Agent: {real_headers['User-Agent'][:50]}...")
        print(f"   Cookie数量: {len(real_cookies)}")
    
    def get_video_info_with_real_browser(self, params: dict) -> dict:
        """使用真实浏览器数据获取视频信息"""
        try:
            print("🌐 使用真实浏览器数据请求...")
            
            # 使用真实的API地址
            url = "https://vd6.l.qq.com/proxyhttp"  # 注意这里是vd6，不是vd
            
            # 构建请求数据（保持原有逻辑）
            buid_data = {
                "buid": "vinfoad",
                "adparam": f"pf=in&ad_type=LD|KB|PVL&pf_ex=pc&url=https://v.qq.com/x/cover/{params['coverid']}.html&refer=https://v.qq.com/&ty=web&plugin=1.0.0&coverid={params['coverid']}&vid={params['vid']}&pt=&flowid={params['flowid']}&vptag=www_baidu_com|video:poster_tle&pu=-1&chid=0&adaptor=2&dtype=1&live=0&resp_type=json&guid={params['guid']}&req_type=1&from=0&appversion={params['app_ver']}&uid=0&tkn=&lt=qq&platform={params['platform']}&opid=&atkn=&appid=101483052&tpid=1&rfid=&cKey={params['ckey']}",
                "vinfoparam": f"spsrt=1&charge=0&defaultfmt=auto&otype=ojson&guid={params['guid']}&flowid={params['flowid']}&platform={params['platform']}&sdtfrom=v1010&defnpayver=1&appVer={params['app_ver']}&host=v.qq.com&ehost=https://v.qq.com/x/cover/{params['coverid']}.html&refer=v.qq.com&sphttps=1&tm={params['_rnd']}&spwm=4&vid={params['vid']}&defn=fhd&fhdswitch=0&show1080p=1&isHLS=1&dtype=3&sphls=2&spgzip=1&dlver=2&drm=32&hdcp=0&spau=1&spaudio=15&defsrc=2&encryptVer=9.1&cKey={params['ckey']}&fp2p=1&spadseg=3"
            }
            
            print(f"📡 请求URL: {url}")
            print(f"📦 请求数据长度: {len(json.dumps(buid_data))}")
            
            # 发送请求
            response = self.session.post(url, json=buid_data, timeout=30)
            
            print(f"📊 响应状态: {response.status_code}")
            print(f"📊 响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 获取视频信息成功")
                
                # 详细分析响应
                print("🔍 响应分析:")
                print(f"   响应键: {list(result.keys())}")
                
                vinfo = result.get('vinfo', '')
                if vinfo:
                    print(f"   vinfo长度: {len(vinfo)}")
                    print(f"   vinfo前200字符: {vinfo[:200]}")
                    
                    # 检查是否还有错误
                    if isinstance(vinfo, str):
                        try:
                            vinfo_dict = json.loads(vinfo)
                            if 'msg' in vinfo_dict:
                                print(f"⚠️ 仍有错误: {vinfo_dict['msg']}")
                            else:
                                print("🎉 没有错误信息！可能成功了！")
                        except:
                            print("📄 vinfo不是JSON格式，可能包含视频数据")
                
                return result
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                print(f"❌ 响应内容: {response.text[:500]}")
                return None
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
            return None
    
    def crawl_video_with_real_browser(self, video_url: str, output_path: str = None) -> bool:
        """使用真实浏览器数据爬取视频"""
        try:
            print("🚀 使用真实浏览器数据爬取视频...")
            print(f"🎯 目标: {video_url}")
            
            # 1. 提取视频信息
            video_info = self.extract_video_info(video_url)
            if not video_info:
                return False
            
            print(f"✅ 视频信息: vid={video_info['vid']}, coverid={video_info['coverid']}")
            
            # 2. 生成参数
            params = self.generate_params(video_info['vid'], video_info['coverid'])
            if not params:
                return False
            
            print("✅ 参数生成完成")
            
            # 3. 使用真实浏览器数据获取视频信息
            video_data = self.get_video_info_with_real_browser(params)
            if not video_data:
                return False
            
            # 4. 解析视频URL
            video_urls = self.parse_video_urls(video_data)
            if not video_urls:
                print("❌ 未找到视频URL")
                return False
            
            print(f"🎉 找到 {len(video_urls)} 个视频URL")
            
            # 5. 下载视频
            if not output_path:
                output_path = f"real_browser_video_{int(time.time())}.mp4"
            
            return self.download_video(video_urls[0], output_path)
            
        except Exception as e:
            print(f"❌ 爬取失败: {e}")
            return False

def test_with_real_browser_data():
    """使用真实浏览器数据测试"""
    print("🧪 使用真实浏览器数据测试")
    print("=" * 50)
    
    # 创建真实浏览器爬虫
    spider = RealBrowserSpider()
    
    # 测试视频
    test_url = "https://v.qq.com/x/cover/qwzpd184if155sa/p00156giz3p.html"
    
    print(f"🎯 测试视频: {test_url}")
    print("💡 这次使用的是你提供的真实浏览器数据！")
    
    # 开始测试
    success = spider.crawl_video_with_real_browser(test_url, "real_browser_test.mp4")
    
    if success:
        print("🎉 成功！真实浏览器数据解决了问题！")
        print("✅ 视频已下载到: real_browser_test.mp4")
    else:
        print("❌ 仍然失败")
        print("💡 可能需要:")
        print("1. 更新Cookie（Cookie可能过期）")
        print("2. 检查POST请求的完整数据")
        print("3. 分析是否还缺少其他参数")

def compare_requests():
    """对比原始爬虫和真实浏览器数据的差异"""
    print("🔍 对比分析")
    print("=" * 50)
    
    print("📊 原始爬虫 vs 真实浏览器数据:")
    print("\n🤖 原始爬虫:")
    print("   API地址: https://vd.l.qq.com/proxyhttp")
    print("   User-Agent: Chrome/94.0.4606.71")
    print("   Cookie: 基本Cookie")
    
    print("\n🌐 真实浏览器:")
    print("   API地址: https://vd6.l.qq.com/proxyhttp")  # 注意vd6
    print("   User-Agent: Chrome/********* Edge")
    print("   Cookie: 完整的用户会话Cookie")
    print("   额外头: Sec-Ch-Ua, Sec-Fetch-* 等")
    
    print("\n🔑 关键差异:")
    print("1. ✅ API地址更新 (vd6 vs vd)")
    print("2. ✅ 完整的用户会话Cookie")
    print("3. ✅ 最新的浏览器特征")
    print("4. ✅ 完整的安全头")

def main():
    """主函数"""
    print("🎬 真实浏览器数据爬虫测试")
    print("=" * 60)
    
    print("💡 说明:")
    print("基于你提供的真实浏览器请求头和Cookie数据")
    print("这次应该能够成功绕过腾讯的检测！")
    
    # 对比分析
    compare_requests()
    
    # 开始测试
    print("\n" + "=" * 50)
    test_with_real_browser_data()

if __name__ == "__main__":
    main()
