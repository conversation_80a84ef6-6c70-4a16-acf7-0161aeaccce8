# For Unicorn Engine. AUTO-GENERATED FILE, DO NOT EDIT [s390x_const.py]

# S390X CPU

UC_CPU_S390X_Z900 = 0
UC_CPU_S390X_Z900_2 = 1
UC_CPU_S390X_Z900_3 = 2
UC_CPU_S390X_Z800 = 3
UC_CPU_S390X_Z990 = 4
UC_CPU_S390X_Z990_2 = 5
UC_CPU_S390X_Z990_3 = 6
UC_CPU_S390X_Z890 = 7
UC_CPU_S390X_Z990_4 = 8
UC_CPU_S390X_Z890_2 = 9
UC_CPU_S390X_Z990_5 = 10
UC_CPU_S390X_Z890_3 = 11
UC_CPU_S390X_Z9EC = 12
UC_CPU_S390X_Z9EC_2 = 13
UC_CPU_S390X_Z9BC = 14
UC_CPU_S390X_Z9EC_3 = 15
UC_CPU_S390X_Z9BC_2 = 16
UC_CPU_S390X_Z10EC = 17
UC_CPU_S390X_Z10EC_2 = 18
UC_CPU_S390X_Z10BC = 19
UC_CPU_S390X_Z10EC_3 = 20
UC_CPU_S390X_Z10BC_2 = 21
UC_CPU_S390X_Z196 = 22
UC_CPU_S390X_Z196_2 = 23
UC_CPU_S390X_Z114 = 24
UC_CPU_S390X_ZEC12 = 25
UC_CPU_S390X_ZEC12_2 = 26
UC_CPU_S390X_ZBC12 = 27
UC_CPU_S390X_Z13 = 28
UC_CPU_S390X_Z13_2 = 29
UC_CPU_S390X_Z13S = 30
UC_CPU_S390X_Z14 = 31
UC_CPU_S390X_Z14_2 = 32
UC_CPU_S390X_Z14ZR1 = 33
UC_CPU_S390X_GEN15A = 34
UC_CPU_S390X_GEN15B = 35
UC_CPU_S390X_QEMU = 36
UC_CPU_S390X_MAX = 37
UC_CPU_S390X_ENDING = 38

# S390X registers

UC_S390X_REG_INVALID = 0

# General purpose registers
UC_S390X_REG_R0 = 1
UC_S390X_REG_R1 = 2
UC_S390X_REG_R2 = 3
UC_S390X_REG_R3 = 4
UC_S390X_REG_R4 = 5
UC_S390X_REG_R5 = 6
UC_S390X_REG_R6 = 7
UC_S390X_REG_R7 = 8
UC_S390X_REG_R8 = 9
UC_S390X_REG_R9 = 10
UC_S390X_REG_R10 = 11
UC_S390X_REG_R11 = 12
UC_S390X_REG_R12 = 13
UC_S390X_REG_R13 = 14
UC_S390X_REG_R14 = 15
UC_S390X_REG_R15 = 16

# Floating point registers
UC_S390X_REG_F0 = 17
UC_S390X_REG_F1 = 18
UC_S390X_REG_F2 = 19
UC_S390X_REG_F3 = 20
UC_S390X_REG_F4 = 21
UC_S390X_REG_F5 = 22
UC_S390X_REG_F6 = 23
UC_S390X_REG_F7 = 24
UC_S390X_REG_F8 = 25
UC_S390X_REG_F9 = 26
UC_S390X_REG_F10 = 27
UC_S390X_REG_F11 = 28
UC_S390X_REG_F12 = 29
UC_S390X_REG_F13 = 30
UC_S390X_REG_F14 = 31
UC_S390X_REG_F15 = 32
UC_S390X_REG_F16 = 33
UC_S390X_REG_F17 = 34
UC_S390X_REG_F18 = 35
UC_S390X_REG_F19 = 36
UC_S390X_REG_F20 = 37
UC_S390X_REG_F21 = 38
UC_S390X_REG_F22 = 39
UC_S390X_REG_F23 = 40
UC_S390X_REG_F24 = 41
UC_S390X_REG_F25 = 42
UC_S390X_REG_F26 = 43
UC_S390X_REG_F27 = 44
UC_S390X_REG_F28 = 45
UC_S390X_REG_F29 = 46
UC_S390X_REG_F30 = 47
UC_S390X_REG_F31 = 48

# Access registers
UC_S390X_REG_A0 = 49
UC_S390X_REG_A1 = 50
UC_S390X_REG_A2 = 51
UC_S390X_REG_A3 = 52
UC_S390X_REG_A4 = 53
UC_S390X_REG_A5 = 54
UC_S390X_REG_A6 = 55
UC_S390X_REG_A7 = 56
UC_S390X_REG_A8 = 57
UC_S390X_REG_A9 = 58
UC_S390X_REG_A10 = 59
UC_S390X_REG_A11 = 60
UC_S390X_REG_A12 = 61
UC_S390X_REG_A13 = 62
UC_S390X_REG_A14 = 63
UC_S390X_REG_A15 = 64
UC_S390X_REG_PC = 65
UC_S390X_REG_PSWM = 66
UC_S390X_REG_ENDING = 67

# Alias registers
