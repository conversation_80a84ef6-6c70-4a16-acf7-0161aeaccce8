"""
应用配置管理
"""

from pydantic_settings import BaseSettings
from typing import Optional
import os


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = "企业级视频平台"
    app_version: str = "1.0.0"
    debug: bool = False
    secret_key: str = "your-secret-key-here"
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    # 数据库配置
    database_url: str = "sqlite:///./video_platform.db"
    
    # Redis配置
    redis_url: str = "redis://localhost:6379/0"
    
    # JWT配置
    jwt_secret_key: str = "your-jwt-secret-key"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    
    # 爬虫配置
    spider_user_agent: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
    spider_timeout: int = 30
    spider_retry_times: int = 3
    spider_download_path: str = "./downloads"
    
    # 文件存储配置
    upload_path: str = "./uploads"
    max_file_size: str = "100MB"
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "./logs/app.log"
    
    # Celery配置
    celery_broker_url: str = "redis://localhost:6379/1"
    celery_result_backend: str = "redis://localhost:6379/2"
    
    # 监控配置
    enable_metrics: bool = True
    metrics_port: int = 9090
    
    # 开发配置
    reload: bool = False
    workers: int = 1
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 创建全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings


# 数据库URL处理
def get_database_url() -> str:
    """获取数据库连接URL"""
    return settings.database_url


# 确保必要目录存在
def ensure_directories():
    """确保必要的目录存在"""
    directories = [
        settings.spider_download_path,
        settings.upload_path,
        os.path.dirname(settings.log_file) if settings.log_file else "./logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 应用启动时调用
ensure_directories()
