# 🎯 SQLAlchemy 零基础学习指南

## 📋 学习策略：从例子开始，理论跟上

### 🚀 第一步：理解什么是ORM (5分钟)

**简单理解**：
- **传统方式**：写SQL语句 → `SELECT * FROM users WHERE id = 1`
- **ORM方式**：写Python代码 → `User.query.filter_by(id=1).first()`

**好处**：不用写复杂的SQL，用Python对象操作数据库

---

## 🎯 第二步：5分钟快速上手

### 最简单的例子
```python
# 1. 创建一个用户表
class User:
    id = 主键
    name = 姓名
    email = 邮箱

# 2. 操作数据
user = User(name="张三", email="<EMAIL>")  # 创建用户
user.save()  # 保存到数据库
users = User.query.all()  # 查询所有用户
```

### 对应的SQL (你不用写，但要理解)
```sql
CREATE TABLE users (id INTEGER, name VARCHAR, email VARCHAR);
INSERT INTO users (name, email) VALUES ('张三', 'zhang<PERSON>@qq.com');
SELECT * FROM users;
```

---

## 🛠️ 第三步：动手练习 (30分钟)

### 练习1：创建第一个模型 (10分钟)
```python
from sqlalchemy import Column, Integer, String
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class Student(Base):
    __tablename__ = 'students'  # 表名
    
    id = Column(Integer, primary_key=True)  # 主键
    name = Column(String(50))  # 姓名，最长50字符
    age = Column(Integer)  # 年龄
```

**理解要点**：
- `Base` = 所有表的基础类
- `__tablename__` = 数据库中的表名
- `Column` = 表的列
- `primary_key=True` = 主键

### 练习2：表关系 (20分钟)
```python
class Class(Base):
    __tablename__ = 'classes'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50))  # 班级名称

class Student(Base):
    __tablename__ = 'students'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50))
    class_id = Column(Integer, ForeignKey('classes.id'))  # 外键
    
    # 关系：一个学生属于一个班级
    class_info = relationship("Class")
```

**理解要点**：
- `ForeignKey` = 外键，连接两个表
- `relationship` = 定义表之间的关系
- 一对多关系：一个班级有多个学生

---

## 📖 第四步：理论补充 (只看需要的)

### 数据库设计三大范式 (简化版)
1. **第一范式**：每个字段不能再分割
   - ❌ 错误：`name = "张三,李四"`
   - ✅ 正确：分别存储每个人

2. **第二范式**：每个表只描述一种事物
   - ❌ 错误：学生表里放班级详细信息
   - ✅ 正确：学生表 + 班级表

3. **第三范式**：字段不依赖于其他非主键字段
   - ❌ 错误：学生表里既有班级ID又有班级名称
   - ✅ 正确：只存班级ID，班级名称在班级表里

### 常用字段类型
```python
Integer        # 整数
String(50)     # 字符串，最长50字符
Text           # 长文本
Boolean        # 布尔值 True/False
DateTime       # 日期时间
Float          # 小数
```

---

## 🎯 第五步：实战练习

### 为视频平台设计数据库
```python
# 用户表
class User(Base):
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True)  # 用户名唯一
    email = Column(String(100), unique=True)    # 邮箱唯一
    password = Column(String(255))              # 密码
    created_at = Column(DateTime, default=datetime.now)

# 视频表
class Video(Base):
    __tablename__ = 'videos'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(255))           # 标题
    url = Column(String(500))             # 视频链接
    duration = Column(Integer)            # 时长(秒)
    user_id = Column(Integer, ForeignKey('users.id'))  # 上传者
    
    # 关系：视频属于某个用户
    uploader = relationship("User")

# 下载历史表
class DownloadHistory(Base):
    __tablename__ = 'download_history'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'))
    video_id = Column(Integer, ForeignKey('videos.id'))
    download_time = Column(DateTime, default=datetime.now)
    
    # 关系
    user = relationship("User")
    video = relationship("Video")
```

---

## ✅ 学习检查清单

### 基础概念 (必须理解)
- [ ] 知道什么是ORM
- [ ] 理解表、行、列的概念
- [ ] 知道主键和外键的作用
- [ ] 理解一对多关系

### 实践技能 (必须会做)
- [ ] 能创建简单的数据模型
- [ ] 能定义表之间的关系
- [ ] 能理解字段类型的选择
- [ ] 能设计简单的数据库结构

### 进阶理解 (慢慢掌握)
- [ ] 理解数据库范式
- [ ] 知道索引的作用
- [ ] 了解查询优化

---

## 🚀 下一步行动

1. **立即行动** (10分钟)：
   - 复制上面的Student例子
   - 在Python中运行
   - 看看能否创建表

2. **深入练习** (20分钟)：
   - 修改视频平台的数据库设计
   - 添加你认为需要的字段
   - 思考表之间的关系

3. **理论补充** (10分钟)：
   - 只有遇到问题时才去查官方文档
   - 重点看你正在使用的功能

**记住**：先做再学，遇到问题再查资料！
