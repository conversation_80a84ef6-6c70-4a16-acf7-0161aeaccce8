"""
视频管理API
"""

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional

from ..database import get_db
from ..models.user import User
from ..models.video import Video, DownloadHistory, VideoCollection
from ..schemas.video import (
    VideoCreate, VideoUpdate, VideoResponse, VideoList,
    DownloadHistoryCreate, DownloadHistoryResponse,
    VideoCollectionCreate, VideoCollectionResponse
)
from ..api.auth import get_current_active_user

router = APIRouter()


@router.post("/", response_model=VideoResponse)
async def create_video(
    video_data: VideoCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """创建视频记录"""
    # 检查视频URL是否已存在
    existing_video = db.query(Video).filter(Video.original_url == video_data.original_url).first()
    if existing_video:
        raise HTTPException(status_code=400, detail="视频URL已存在")
    
    # 创建视频记录
    db_video = Video(**video_data.dict())
    db.add(db_video)
    db.commit()
    db.refresh(db_video)
    
    return db_video


@router.get("/", response_model=VideoList)
async def get_videos(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    category: Optional[str] = Query(None, description="分类筛选"),
    is_free: Optional[bool] = Query(None, description="是否免费"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db)
):
    """获取视频列表"""
    # 构建查询
    query = db.query(Video).filter(Video.is_available == True)
    
    # 应用筛选条件
    if category:
        query = query.filter(Video.category == category)
    if is_free is not None:
        query = query.filter(Video.is_free == is_free)
    if search:
        query = query.filter(Video.title.contains(search))
    
    # 计算偏移量
    offset = (page - 1) * size
    
    # 执行查询
    videos = query.offset(offset).limit(size).all()
    total = query.count()
    
    return VideoList(
        videos=videos,
        total=total,
        page=page,
        size=size
    )


@router.get("/{video_id}", response_model=VideoResponse)
async def get_video(
    video_id: int,
    db: Session = Depends(get_db)
):
    """获取视频详情"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    
    return video


@router.put("/{video_id}", response_model=VideoResponse)
async def update_video(
    video_id: int,
    video_update: VideoUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新视频信息 (需要管理员权限)"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    
    # 更新视频信息
    for field, value in video_update.dict(exclude_unset=True).items():
        setattr(video, field, value)
    
    db.commit()
    db.refresh(video)
    return video


@router.delete("/{video_id}")
async def delete_video(
    video_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """删除视频 (需要管理员权限)"""
    if not current_user.is_superuser:
        raise HTTPException(status_code=403, detail="权限不足")
    
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    
    db.delete(video)
    db.commit()
    
    return {"message": "视频删除成功"}


@router.post("/{video_id}/download", response_model=DownloadHistoryResponse)
async def download_video(
    video_id: int,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """下载视频"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    
    # 检查是否已有下载记录
    existing_download = db.query(DownloadHistory).filter(
        DownloadHistory.user_id == current_user.id,
        DownloadHistory.video_id == video_id,
        DownloadHistory.status.in_(["pending", "downloading"])
    ).first()
    
    if existing_download:
        raise HTTPException(status_code=400, detail="视频正在下载中")
    
    # 创建下载记录
    download_history = DownloadHistory(
        user_id=current_user.id,
        video_id=video_id,
        status="pending",
        file_name=f"{video.title}.mp4"
    )
    
    db.add(download_history)
    db.commit()
    db.refresh(download_history)
    
    # 添加后台下载任务
    # background_tasks.add_task(download_video_task, download_history.id)
    
    # 更新视频下载次数
    video.download_count += 1
    db.commit()
    
    return download_history


@router.get("/downloads/history", response_model=List[DownloadHistoryResponse])
async def get_download_history(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户下载历史"""
    downloads = db.query(DownloadHistory).filter(
        DownloadHistory.user_id == current_user.id
    ).order_by(DownloadHistory.created_at.desc()).all()
    
    return downloads


@router.post("/{video_id}/collect", response_model=VideoCollectionResponse)
async def collect_video(
    video_id: int,
    collection_data: VideoCollectionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """收藏视频"""
    video = db.query(Video).filter(Video.id == video_id).first()
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    
    # 检查是否已收藏
    existing_collection = db.query(VideoCollection).filter(
        VideoCollection.user_id == current_user.id,
        VideoCollection.video_id == video_id
    ).first()
    
    if existing_collection:
        raise HTTPException(status_code=400, detail="视频已收藏")
    
    # 创建收藏记录
    collection = VideoCollection(
        user_id=current_user.id,
        video_id=video_id,
        collection_name=collection_data.collection_name,
        notes=collection_data.notes
    )
    
    db.add(collection)
    db.commit()
    db.refresh(collection)
    
    return collection


@router.get("/collections/", response_model=List[VideoCollectionResponse])
async def get_collections(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """获取用户收藏列表"""
    collections = db.query(VideoCollection).filter(
        VideoCollection.user_id == current_user.id,
        VideoCollection.is_favorite == True
    ).order_by(VideoCollection.created_at.desc()).all()
    
    return collections
