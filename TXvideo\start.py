#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫框架 - 快速启动脚本
"""

import os
import sys

def show_banner():
    """显示欢迎横幅"""
    print("=" * 60)
    print("🎬 腾讯视频爬虫框架")
    print("=" * 60)
    print("📝 作者: AI Assistant")
    print("📅 版本: v1.0.0")
    print("⚠️  仅供学习研究使用，请遵守相关法律法规")
    print("=" * 60)

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖包...")
    
    required_packages = [
        ('requests', 'requests'),
        ('execjs', 'PyExecJS'),
        ('Crypto', 'pycryptodome'),
    ]
    
    missing_packages = []
    
    for package_name, pip_name in required_packages:
        try:
            __import__(package_name)
            print(f"✅ {pip_name}")
        except ImportError:
            print(f"❌ {pip_name} - 未安装")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n⚠️ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def show_menu():
    """显示主菜单"""
    print("\n📋 选择功能:")
    print("1. 🎯 快速下载单个视频")
    print("2. � 智能寻找可下载视频")
    print("3. �📊 测试框架功能")
    print("4. 🔍 提取视频信息")
    print("5. 🧪 运行完整测试")
    print("6. 📚 查看使用指南")
    print("0. 🚪 退出")
    print("-" * 40)

def quick_download():
    """快速下载功能"""
    print("\n🎯 快速下载视频")
    print("-" * 40)
    
    try:
        from tx_video_spider import TencentVideoSpider
        
        # 获取用户输入
        video_url = input("请输入腾讯视频URL: ").strip()
        if not video_url:
            print("❌ URL不能为空")
            return
        
        output_file = input("请输入输出文件名 (默认: video.mp4): ").strip()
        if not output_file:
            output_file = "video.mp4"
        
        # 确保输出目录存在
        output_dir = "./downloads/"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        output_path = os.path.join(output_dir, output_file)
        
        print(f"\n🚀 开始下载...")
        print(f"URL: {video_url}")
        print(f"输出: {output_path}")
        
        # 创建爬虫并下载
        spider = TencentVideoSpider()
        success = spider.crawl_video(video_url, output_path)
        
        if success:
            print("🎉 下载成功！")
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path)
                print(f"文件大小: {file_size / (1024*1024):.2f} MB")
        else:
            print("❌ 下载失败，请检查URL或网络连接")
            
    except ImportError:
        print("❌ 无法导入爬虫模块，请检查文件完整性")
    except Exception as e:
        print(f"❌ 下载异常: {e}")

def smart_find_videos():
    """智能寻找可下载的视频"""
    print("\n🔍 智能寻找可下载视频")
    print("-" * 40)

    # 一些可能免费的腾讯视频URL（需要实际验证）
    test_urls = [
        # 新闻类视频（通常免费）
        "https://v.qq.com/x/cover/mzc00200news001.html",
        "https://v.qq.com/x/cover/mzc00200news002.html",

        # 预告片类（通常免费）
        "https://v.qq.com/x/cover/mzc00200trailer001.html",
        "https://v.qq.com/x/cover/mzc00200trailer002.html",

        # 短视频类
        "https://v.qq.com/x/cover/mzc00200short001.html",
        "https://v.qq.com/x/cover/mzc00200short002.html",
    ]

    print("🔍 正在测试预设的可能免费视频...")
    print("注意：这些URL可能不存在，仅作为格式示例")

    try:
        from tx_video_spider import TencentVideoSpider
        spider = TencentVideoSpider()

        found_working = False

        for i, url in enumerate(test_urls, 1):
            print(f"\n📋 测试 {i}: {url}")

            try:
                video_info = spider.extract_video_info(url)
                if video_info.get('vid'):
                    params = spider.generate_params(video_info['vid'], video_info['coverid'])
                    video_data = spider.get_video_info(params)

                    if video_data:
                        vinfo = video_data.get('vinfo', '')
                        if '外星人' not in str(vinfo) and 'msg' not in str(vinfo):
                            print(f"🎉 找到可能可用的视频: {url}")

                            # 尝试下载
                            choice = input("是否尝试下载这个视频？(y/n): ").strip().lower()
                            if choice == 'y':
                                output_file = f"found_video_{i}.mp4"
                                output_path = os.path.join("./downloads/", output_file)

                                success = spider.crawl_video(url, output_path)
                                if success:
                                    print(f"✅ 下载成功: {output_path}")
                                    found_working = True
                                    break
                                else:
                                    print("❌ 下载失败")
                        else:
                            print("❌ 视频访问受限")
                    else:
                        print("❌ 获取视频数据失败")
                else:
                    print("❌ 无法提取视频信息")

            except Exception as e:
                print(f"❌ 测试异常: {e}")

        if not found_working:
            print("\n💡 建议的解决方案:")
            print("1. 🌐 在浏览器中访问 https://v.qq.com")
            print("2. 🔍 寻找以下类型的免费视频:")
            print("   - 新闻短视频")
            print("   - 电影预告片")
            print("   - 公开宣传片")
            print("   - 综艺节目片段")
            print("3. 📋 复制视频URL，格式如:")
            print("   https://v.qq.com/x/cover/xxx/yyy.html")
            print("4. 🎯 使用快速下载功能测试")

            print("\n🔧 或者尝试以下技术方案:")
            print("1. 使用VPN或代理服务器")
            print("2. 添加腾讯视频的登录Cookie")
            print("3. 更新cKey生成算法")

    except ImportError:
        print("❌ 无法导入爬虫模块")
    except Exception as e:
        print(f"❌ 搜索异常: {e}")

def test_framework():
    """测试框架功能"""
    print("\n📊 测试框架功能")
    print("-" * 40)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_spider.py"], 
                              capture_output=True, text=True, cwd=".")
        
        if result.returncode == 0:
            print("✅ 框架测试通过")
            print(result.stdout)
        else:
            print("❌ 框架测试失败")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def extract_info():
    """提取视频信息"""
    print("\n🔍 提取视频信息")
    print("-" * 40)
    
    try:
        from tx_video_spider import TencentVideoSpider
        
        video_url = input("请输入腾讯视频URL: ").strip()
        if not video_url:
            print("❌ URL不能为空")
            return
        
        spider = TencentVideoSpider()
        
        # 提取基本信息
        print("📋 提取基本信息...")
        video_info = spider.extract_video_info(video_url)
        
        if video_info:
            print(f"✅ VID: {video_info.get('vid', 'N/A')}")
            print(f"✅ Cover ID: {video_info.get('coverid', 'N/A')}")
            
            # 生成参数
            print("\n🔧 生成请求参数...")
            params = spider.generate_params(video_info['vid'], video_info['coverid'])
            
            if params:
                print(f"✅ GUID: {params.get('guid', 'N/A')}")
                print(f"✅ Platform: {params.get('platform', 'N/A')}")
                print(f"✅ cKey: {params.get('ckey', 'N/A')[:50]}...")
                
                # 获取视频信息
                print("\n📊 获取视频详细信息...")
                video_data = spider.get_video_info(params)
                
                if video_data:
                    print("✅ 获取成功")
                    video_urls = spider.parse_video_urls(video_data)
                    print(f"🎬 找到 {len(video_urls)} 个视频流")
                    
                    for i, url in enumerate(video_urls[:3], 1):
                        print(f"  {i}. {url[:80]}...")
                else:
                    print("❌ 获取视频信息失败")
            else:
                print("❌ 参数生成失败")
        else:
            print("❌ 提取视频信息失败")
            
    except Exception as e:
        print(f"❌ 提取异常: {e}")

def run_full_test():
    """运行完整测试"""
    print("\n🧪 运行完整测试")
    print("-" * 40)
    
    test_files = [
        ("基础功能测试", "test_spider.py"),
        ("高级测试", "advanced_test.py"),
        ("调试测试", "debug_test.py"),
    ]
    
    for test_name, test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n📋 {test_name}...")
            try:
                import subprocess
                result = subprocess.run([sys.executable, test_file], 
                                      capture_output=True, text=True, cwd=".")
                
                if result.returncode == 0:
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
                    
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
        else:
            print(f"⚠️ {test_file} 文件不存在")

def show_guide():
    """显示使用指南"""
    print("\n📚 使用指南")
    print("-" * 40)
    
    guide_files = ["使用指南.md", "README.md", "FINAL_SUMMARY.md"]
    
    for guide_file in guide_files:
        if os.path.exists(guide_file):
            print(f"📖 {guide_file} - 可用")
        else:
            print(f"❌ {guide_file} - 不存在")
    
    print("\n💡 快速使用:")
    print("1. 确保所有依赖已安装: pip install -r requirements.txt")
    print("2. 运行交互式示例: python example.py")
    print("3. 或直接使用本启动脚本的快速下载功能")
    
    print("\n🔍 基本代码示例:")
    print("```python")
    print("from tx_video_spider import TencentVideoSpider")
    print("spider = TencentVideoSpider()")
    print("success = spider.crawl_video('视频URL', '输出文件.mp4')")
    print("```")

def main():
    """主函数"""
    show_banner()
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择 (0-5): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                quick_download()
            elif choice == '2':
                smart_find_videos()
            elif choice == '3':
                test_framework()
            elif choice == '4':
                extract_info()
            elif choice == '5':
                run_full_test()
            elif choice == '6':
                show_guide()
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出程序")
            break
        except Exception as e:
            print(f"❌ 程序异常: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
