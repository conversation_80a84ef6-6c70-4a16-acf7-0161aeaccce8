"""
视频相关数据模型
"""

from sqlalchemy import Column, String, Integer, Boolean, Text, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship
from .base import BaseModel


class Video(BaseModel):
    """视频信息模型"""
    
    __tablename__ = "videos"
    
    # 基本信息
    title = Column(String(255), nullable=False, comment="视频标题")
    description = Column(Text, comment="视频描述")
    original_url = Column(String(500), nullable=False, unique=True, comment="原始URL")
    
    # 腾讯视频特有信息
    vid = Column(String(50), comment="视频ID")
    cover_id = Column(String(50), comment="封面ID")
    cover_url = Column(String(500), comment="封面图片URL")
    
    # 视频属性
    duration = Column(Integer, comment="视频时长(秒)")
    file_size = Column(Integer, comment="文件大小(字节)")
    quality = Column(String(20), comment="视频质量")
    format = Column(String(10), comment="视频格式")
    
    # 状态信息
    is_available = Column(Boolean, default=True, comment="是否可用")
    is_free = Column(Boolean, default=False, comment="是否免费")
    download_count = Column(Integer, default=0, comment="下载次数")
    
    # 元数据
    metadata = Column(JSON, comment="额外元数据")
    tags = Column(String(500), comment="标签(逗号分隔)")
    category = Column(String(50), comment="分类")
    
    # 关联关系
    download_histories = relationship(
        "DownloadHistory", 
        back_populates="video",
        cascade="all, delete-orphan"
    )
    collections = relationship(
        "VideoCollection", 
        back_populates="video",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self):
        return f"<Video(id={self.id}, title='{self.title}', vid='{self.vid}')>"
    
    @property
    def duration_formatted(self):
        """格式化的时长"""
        if not self.duration:
            return "未知"
        
        hours = self.duration // 3600
        minutes = (self.duration % 3600) // 60
        seconds = self.duration % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"
    
    @property
    def file_size_formatted(self):
        """格式化的文件大小"""
        if not self.file_size:
            return "未知"
        
        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"


class DownloadHistory(BaseModel):
    """下载历史模型"""
    
    __tablename__ = "download_histories"
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, comment="视频ID")
    
    # 下载信息
    download_url = Column(String(500), comment="下载链接")
    local_path = Column(String(500), comment="本地存储路径")
    file_name = Column(String(255), comment="文件名")
    
    # 下载状态
    status = Column(String(20), default="pending", comment="下载状态")  # pending, downloading, completed, failed
    progress = Column(Float, default=0.0, comment="下载进度(0-100)")
    error_message = Column(Text, comment="错误信息")
    
    # 下载统计
    download_speed = Column(Float, comment="下载速度(KB/s)")
    downloaded_size = Column(Integer, default=0, comment="已下载大小(字节)")
    total_size = Column(Integer, comment="总大小(字节)")
    
    # 关联关系
    user = relationship("User", back_populates="download_histories")
    video = relationship("Video", back_populates="download_histories")
    
    def __repr__(self):
        return f"<DownloadHistory(id={self.id}, user_id={self.user_id}, video_id={self.video_id}, status='{self.status}')>"
    
    @property
    def progress_percentage(self):
        """进度百分比"""
        return f"{self.progress:.1f}%"
    
    @property
    def is_completed(self):
        """是否下载完成"""
        return self.status == "completed"
    
    @property
    def is_failed(self):
        """是否下载失败"""
        return self.status == "failed"


class VideoCollection(BaseModel):
    """用户视频收藏模型"""
    
    __tablename__ = "video_collections"
    
    # 关联信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    video_id = Column(Integer, ForeignKey("videos.id"), nullable=False, comment="视频ID")
    
    # 收藏信息
    collection_name = Column(String(100), default="默认收藏", comment="收藏夹名称")
    notes = Column(Text, comment="备注")
    is_favorite = Column(Boolean, default=True, comment="是否收藏")
    
    # 关联关系
    user = relationship("User", back_populates="video_collections")
    video = relationship("Video", back_populates="collections")
    
    def __repr__(self):
        return f"<VideoCollection(id={self.id}, user_id={self.user_id}, video_id={self.video_id})>"
