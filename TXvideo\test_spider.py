#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯视频爬虫测试脚本
"""

import sys
import os

def test_imports():
    """测试所有依赖包是否正确导入"""
    print("🔍 测试依赖包导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        import execjs
        print("✅ execjs 导入成功")
    except ImportError as e:
        print(f"❌ execjs 导入失败: {e}")
        return False
    
    try:
        from Crypto.Cipher import AES
        print("✅ pycryptodome 导入成功")
    except ImportError as e:
        print(f"❌ pycryptodome 导入失败: {e}")
        return False
    
    return True

def test_spider_import():
    """测试爬虫类导入"""
    print("\n🔍 测试爬虫类导入...")
    
    try:
        from tx_video_spider import TencentVideoSpider
        print("✅ TencentVideoSpider 导入成功")
        return True
    except ImportError as e:
        print(f"❌ TencentVideoSpider 导入失败: {e}")
        return False

def test_spider_basic_functions():
    """测试爬虫基本功能"""
    print("\n🔍 测试爬虫基本功能...")
    
    try:
        from tx_video_spider import TencentVideoSpider
        spider = TencentVideoSpider()
        print("✅ 爬虫实例创建成功")
        
        # 测试URL解析
        test_url = "https://v.qq.com/x/cover/mzc00200example/example123.html"
        video_info = spider.extract_video_info(test_url)
        print(f"✅ URL解析功能正常: {video_info}")
        
        # 测试参数生成
        if video_info.get('vid'):
            params = spider.generate_params(video_info['vid'], video_info['coverid'])
            if params.get('ckey'):
                print("✅ 参数生成功能正常")
            else:
                print("⚠️ 参数生成可能有问题")
        
        return True
        
    except Exception as e:
        print(f"❌ 爬虫功能测试失败: {e}")
        return False

def test_javascript_execution():
    """测试JavaScript执行环境"""
    print("\n🔍 测试JavaScript执行环境...")
    
    try:
        import execjs
        
        # 测试基本JS执行
        js_code = '''
        function test() {
            return "Hello from JavaScript!";
        }
        '''
        
        ctx = execjs.compile(js_code)
        result = ctx.call('test')
        
        if result == "Hello from JavaScript!":
            print("✅ JavaScript执行环境正常")
            return True
        else:
            print("❌ JavaScript执行结果异常")
            return False
            
    except Exception as e:
        print(f"❌ JavaScript执行环境测试失败: {e}")
        return False

def test_crypto_functions():
    """测试加密功能"""
    print("\n🔍 测试加密功能...")
    
    try:
        from tx_video_spider import TencentVideoSpider
        spider = TencentVideoSpider()
        
        # 测试AES加密
        test_data = "test_encryption_data"
        encrypted = spider.aes_encrypt(test_data)
        
        if encrypted and len(encrypted) > 0:
            print("✅ AES加密功能正常")
            
            # 测试qa值生成
            qa_value = spider.create_qa(test_data)
            print(f"✅ qa值生成功能正常: {qa_value}")
            
            return True
        else:
            print("❌ AES加密功能异常")
            return False
            
    except Exception as e:
        print(f"❌ 加密功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试腾讯视频爬虫框架...")
    print("=" * 50)
    
    tests = [
        ("依赖包导入", test_imports),
        ("爬虫类导入", test_spider_import),
        ("JavaScript执行", test_javascript_execution),
        ("加密功能", test_crypto_functions),
        ("爬虫基本功能", test_spider_basic_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！框架可以正常使用。")
        print("\n💡 使用建议:")
        print("1. 运行 'python example.py' 开始使用")
        print("2. 准备一个真实的腾讯视频URL进行测试")
        print("3. 注意遵守相关法律法规")
    else:
        print("⚠️ 部分测试失败，请检查依赖安装或代码问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
